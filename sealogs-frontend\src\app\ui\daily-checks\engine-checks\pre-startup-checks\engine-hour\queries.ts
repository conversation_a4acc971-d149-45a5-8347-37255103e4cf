import gql from 'graphql-tag'

// GET_SECTION_MEMBER_COMMENTS
export const ReadSectionMemberComments = gql`
    query ReadSectionMemberComments(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SectionMemberCommentFilterFields = {}
    ) {
        readSectionMemberComments(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                commentType
                fieldName
                comment
                logBookEntrySectionID
                hideComment
            }
        }
    }
`

// GET_ENGINES
export const ReadEngines = gql`
    query ReadEngines($id: [ID]!, $filter: EngineStartStopFilterFields = {}) {
        readEngines(filter: { id: { in: $id } }) {
            nodes {
                id
                title
                type
                currentHours
                identifier
                isPrimary
                maxPower
                driveType
                positionOnVessel
                archived
                componentCategory
                make
                model
                kW
                kVA
                engineStartStops(sort: { created: ASC }, filter: $filter) {
                    nodes {
                        id
                        hoursStart
                        hoursEnd
                        totalHours
                        timeStart
                        timeEnd
                        logBookEntrySection {
                            logBookEntryID
                        }
                    }
                }
            }
        }
    }
`
