{"name": "playright-test", "version": "1.0.0", "main": "index.js", "scripts": {"test": "playwright test", "test:webkit": "playwright test --project=webkit", "test:safari": "playwright test --project=webkit --project=\"Mobile Safari\"", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:sealogs": "playwright test tests/sealogs.spec.ts", "test:sealogs-safari": "playwright test tests/sealogs.spec.ts --project=webkit --headed", "report": "playwright show-report"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.4", "devDependencies": {"@playwright/test": "^1.54.1", "@types/node": "^24.0.15"}, "directories": {"test": "tests"}, "description": ""}