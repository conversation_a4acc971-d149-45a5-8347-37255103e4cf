'use client'

import { useTheme } from "next-themes";
import { cn } from "../utils";

const React = require('react')

export function SealogsVesselsSelectedIcon({ ...props }) {
//  return /*#__PURE__*/ React.createElement(
//    'svg',
//    Object.assign(
//      {
//        'className': cn('w-12 h-12 ring-1 p-0.5 shrink-0 rounded-full bg-[#fff]', className),
//        'xmlns': 'http://www.w3.org/2000/svg',
//        'viewBox': '0 0 126.43 121.34',
//        'fill': 'currentColor',
//        'aria-hidden': 'true',
//        'data-slot': 'icon',
//      },
//      props
//    ),
//    /*#__PURE__*/ React.createElement('path', {
//        'd': 'M19.51,82.18c-.07.11-.08.24-.03.36l6.23,15.82c.06.16.21.26.38.26h92.06c.19,0,.35-.13.4-.31l7.14-27.92c.03-.13,0-.27-.09-.37-.09-.1-.23-.15-.36-.14l-9.48,1.09-6.08-24c-.05-.2-.25-.33-.46-.31l-55.68,7.88c-.13.02-.24.1-.31.21-.06.12-.06.25,0,.37l2.96,6.07c.08.16.25.25.43.23l17.4-2.46,4.21,16.32-47.55,5.46-12.93-47.06c-.06-.22-.28-.35-.51-.29-.22.06-.35.29-.29.51l.9,3.28-15.09,10.09c-.14.1-.21.27-.17.44s.18.29.35.31l18.48,2.15,8.43,30.67-10.03,1.15c-.13.01-.24.09-.31.2Z',
//        'fill': '#fff',
//        'stroke': '#0c2745',
//        'strokeMiterlimit': '10',
//        'strokeWidth': '2px',
//      }),
//    /*#__PURE__*/ React.createElement('polygon', {
//        'points': '2.05 47.97 18.43 37.02 22.08 50.3 2.05 47.97',
//        'fill': '#2a99ea',
//        'stroke': '#0c2745',
//        'strokeMiterlimit': '10',
//        'strokeWidth': '2px',
//      })
//  );
  const { theme, setTheme } = useTheme()

  const getTheme = () => {
    switch (theme) {
      case 'dark':
        return 'Dark'
      case 'light':
        return 'Light'
      default:
        return 'System'
    }
  }

  getTheme()

  if (theme === 'light') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/boat-light-selected.svg',
      alt: 'Boat Light Icon',
      ...props,
    });
  } else if (theme === 'dark') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/boat-selected-dark.svg',
      alt: 'Boat Dark Icon',
      ...props,
    });
  }
}
