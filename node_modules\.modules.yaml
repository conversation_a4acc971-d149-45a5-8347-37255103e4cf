hoistPattern:
  - '*'
hoistedDependencies:
  playwright-core@1.54.1:
    playwright-core: private
  playwright@1.54.1:
    playwright: private
  undici-types@7.8.0:
    undici-types: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Tue, 22 Jul 2025 07:49:06 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - fsevents@2.3.2
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Music\playright test\node_modules\.pnpm
virtualStoreDirMaxLength: 60
