import gql from 'graphql-tag'

// GET_FUELLOGS
export const ReadFuelLogs = gql`
    query ReadFuelLogs($id: [ID]!) {
        readFuelLogs(filter: { id: { in: $id } }) {
            nodes {
                id
                fuelAdded
                fuelBefore
                fuelAfter
                date
                type
                typeID
                fuelTank {
                    id
                    capacity
                    safeFuelCapacity
                    currentLevel
                    title
                }
            }
        }
    }
`
