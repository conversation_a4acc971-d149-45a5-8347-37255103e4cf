'use client'

import { useTheme } from "next-themes"

const React = require('react')

export function SealogsCrewSelectedIcon({ ...props }) {
  //return /*#__PURE__*/ React.createElement(
  //  'svg',
  //  Object.assign(
  //    {
  //      xmlns: 'http://www.w3.org/2000/svg',
  //      viewBox: '0 0 126.43 121.34',
  //      className: 'h-12 w-12 ring-1 p-0.5 rounded-full bg-[#fff]',
  //    },
  //    props,
  //  ),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M62.01,9.87c2.45-.11,4.87.07,7.26.56,5.24,1.01,10.24,2.66,15.02,4.95,1.95,1.04,3.47,2.5,4.54,4.39,1.14,3.08.4,5.66-2.23,7.74-.98.72-1.99,1.39-3.05,2-.05.75-.15,1.5-.33,2.24-.08.23-.21.41-.41.56,1.26,6.76-1.6,10.86-8.58,12.29-7.12,1.25-14.22,1.15-21.3-.32-6.13-1.81-8.61-5.78-7.43-11.89-.38-.92-.6-1.87-.66-2.87-2.49-1.21-4.33-3.02-5.53-5.43-.43-1.65-.26-3.25.5-4.79,1.88-2.49,4.33-4.28,7.35-5.35,4.83-1.86,9.78-3.21,14.86-4.07h0Z',
  //    fill: '#fff',
  //    fillRule: 'evenodd',
  //    stroke: '#0c2745',
  //    strokeMiterlimit: '10',
  //    strokeWidth: '2px',
  //  }),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M62.01,28.37c4.91.07,9.8.32,14.69.74.2.25.31.55.33.91.13.78-.2,1.19-.99,1.24-1.03.25-2.08.42-3.14.5-5.5.11-11.01.11-16.51,0-1.57-.07-3.11-.32-4.62-.74-.06-.3-.11-.61-.17-.91.02-.38.13-.71.33-.99,3.38-.3,6.74-.55,10.07-.74h0Z',
  //    fill: '#0c2745',
  //    fillRule: 'evenodd',
  //  }),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M102.79,34.64c4.67.17,9.19,1.09,13.54,2.76,2.1.7,3.94,1.78,5.53,3.23,2.24,2.99,1.88,5.69-1.07,8.12-.52.38-1.07.72-1.65,1.02-.12.67-.28,1.33-.5,1.97.69,5.22-1.68,8.32-7.1,9.3-1.15.22-2.3.37-3.47.47-2.59.11-5.17.11-7.76,0-2.63-.11-5.1-.74-7.43-1.89-2.79-2.07-3.79-4.75-2.97-8.04-.25-.59-.47-1.19-.66-1.81-1.77-.88-3.07-2.19-3.88-3.94-.5-1.7-.23-3.28.83-4.73,1.78-1.78,3.9-3.07,6.36-3.86,3.35-1.17,6.76-2.03,10.24-2.6h0Z',
  //    fill: '#fff',
  //    fillRule: 'evenodd',
  //    stroke: '#0c2745',
  //    strokeMiterlimit: '10',
  //    strokeWidth: '2px',
  //  }),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M51.44,47.35c8.53-.03,17.06,0,25.59.08,2.74.7,4.41,2.43,5.04,5.2.11,9.25.11,18.49,0,27.74-1.2,3.81-3.82,5.57-7.84,5.28-.38,8.15-.79,16.29-1.24,24.43-.18,1.72-1.08,2.85-2.72,3.38-3.91.11-7.81.11-11.72,0-1.53-.38-2.44-1.34-2.72-2.89-.5-8.2-1-16.4-1.49-24.6-1.03-.28-2.11-.48-3.22-.58-2.82-.95-4.44-2.9-4.87-5.86-.11-8.75-.11-17.5,0-26.25.48-3.1,2.22-5.08,5.2-5.94h0Z',
  //    fill: '#fff',
  //    fillRule: 'evenodd',
  //    stroke: '#0c2745',
  //    strokeMiterlimit: '10',
  //    strokeWidth: '2px',
  //  }),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M100.15,49c4.24-.07,8.48.07,12.71.41.36.42.41.88.17,1.4-.54.19-1.09.33-1.65.41-4.62.32-9.24.32-13.87,0-.68-.05-1.34-.19-1.98-.41-.16-.43-.22-.87-.17-1.32,1.62-.17,3.21-.34,4.79-.5h0Z',
  //    fill: '#0c2745',
  //    fillRule: 'evenodd',
  //  }),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M15.12,63.53c6-.03,12,0,17.99.08,2.83.41,4.46,2.04,4.87,4.87.11,6.55.11,13.1,0,19.65-.64,3.2-2.6,4.8-5.86,4.79-.27,6.17-.62,12.33-1.07,18.49-.37,1.09-1.12,1.78-2.23,2.06-3.03.11-6.05.11-9.08,0-1.78-.66-2.58-1.96-2.39-3.88-.35-5.56-.65-11.11-.91-16.67-3.28.11-5.28-1.43-6.03-4.62-.11-6.66-.11-13.32,0-19.98.5-2.67,2.07-4.27,4.71-4.79h0Z',
  //    fill: '#fff',
  //    fillRule: 'evenodd',
  //    stroke: '#0c2745',
  //    strokeMiterlimit: '10',
  //    strokeWidth: '2px',
  //  }),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M95.19,63.53c6-.03,12,0,17.99.08,2.38.4,3.95,1.75,4.71,4.04.11,7.1.11,14.2,0,21.3-.95,2.69-2.85,4.01-5.7,3.96-.3,5.67-.61,11.34-.91,17,.04,1.77-.76,2.95-2.39,3.55-3.03.11-6.05.11-9.08,0-1.64-.59-2.43-1.78-2.39-3.55-.32-5.67-.68-11.34-1.07-17-3.12.04-5.07-1.44-5.86-4.46-.11-6.77-.11-13.54,0-20.31.57-2.57,2.13-4.12,4.71-4.62h0Z',
  //    fill: '#fff',
  //    fillRule: 'evenodd',
  //    stroke: '#0c2745',
  //    strokeMiterlimit: '10',
  //    strokeWidth: '2px',
  //  }),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M23.2,34.71c4.67.17,9.19,1.09,13.54,2.76,2.1.7,3.94,1.78,5.53,3.23,2.24,2.99,1.88,5.69-1.07,8.12-.52.38-1.07.72-1.65,1.02-.12.67-.28,1.33-.5,1.97.69,5.22-1.68,8.32-7.1,9.3-1.15.22-2.3.37-3.47.47-2.59.11-5.17.11-7.76,0-2.63-.11-5.1-.74-7.43-1.89-2.79-2.07-3.79-4.75-2.97-8.04-.25-.59-.47-1.19-.66-1.81-1.77-.88-3.07-2.19-3.88-3.94-.5-1.7-.23-3.28.83-4.73,1.78-1.78,3.9-3.07,6.36-3.86,3.35-1.17,6.76-2.03,10.24-2.6h0Z',
  //    fill: '#fff',
  //    fillRule: 'evenodd',
  //    stroke: '#0c2745',
  //    strokeMiterlimit: '10',
  //    strokeWidth: '2px',
  //  }),
  //  /*#__PURE__*/ React.createElement('path', {
  //    d: 'M20.56,49.08c4.24-.07,8.48.07,12.71.41.36.42.41.88.17,1.4-.54.19-1.09.33-1.65.41-4.62.32-9.24.32-13.87,0-.68-.05-1.34-.19-1.98-.41-.16-.43-.22-.87-.17-1.32,1.62-.17,3.21-.34,4.79-.5h0Z',
  //    fill: '#0c2745',
  //    fillRule: 'evenodd',
  //  }),
  //);

  const { theme, setTheme } = useTheme()

  const getTheme = () => {
    switch (theme) {
      case 'dark':
        return 'Dark'
      case 'light':
        return 'Light'
      default:
        return 'System'
    }
  }

  getTheme()

  if (theme === 'light') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/crew-selected-light.svg',
      alt: 'Crew Light Icon',
      ...props,
    });
  } else if (theme === 'dark') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/crew-selected-dark.svg',
      alt: 'Crew Dark Icon',
      ...props,
    });
  }
}