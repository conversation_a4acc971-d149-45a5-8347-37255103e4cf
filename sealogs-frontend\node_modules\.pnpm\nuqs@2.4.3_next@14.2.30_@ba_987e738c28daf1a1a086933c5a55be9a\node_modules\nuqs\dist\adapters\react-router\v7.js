'use client';

import { createReactRouterBasedAdapter } from '../../chunk-AAU4ZYLC.js';
import '../../chunk-TCMXVJZC.js';
import '../../chunk-5WWTJYGR.js';
import { useSearchParams, useNavigate } from 'react-router';

var { NuqsAdapter, useOptimisticSearchParams } = createReactRouterBasedAdapter({
  adapter: "react-router-v7",
  useNavigate,
  useSearchParams
});

export { NuqsAdapter, useOptimisticSearchParams };
