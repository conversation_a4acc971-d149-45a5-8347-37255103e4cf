import e,{useContext as t,Fragment as n,useState as r,use<PERSON><PERSON>back as o,useEffect as a,useMemo as s,memo as l,createContext as m}from"react";const c=["light","dark"],i="(prefers-color-scheme: dark)",d="undefined"==typeof window,u=/*#__PURE__*/m(void 0),h={setTheme:e=>{},themes:[]},y=()=>{var e;return null!==(e=t(u))&&void 0!==e?e:h},$=r=>t(u)?/*#__PURE__*/e.createElement(n,null,r.children):/*#__PURE__*/e.createElement(f,r),v=["light","dark"],f=({forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:l=!0,enableColorScheme:m=!0,storageKey:d="theme",themes:h=v,defaultTheme:y=(l?"system":"light"),attribute:$="data-theme",value:f,children:w,nonce:T})=>{const[E,k]=r(()=>S(d,y)),[C,L]=r(()=>S(d)),x=f?Object.values(f):h,I=o(e=>{let t=e;if(!t)return;"system"===e&&l&&(t=p());const r=f?f[t]:t,o=n?b():null,a=document.documentElement;if("class"===$?(a.classList.remove(...x),r&&a.classList.add(r)):r?a.setAttribute($,r):a.removeAttribute($),m){const e=c.includes(y)?y:null,n=c.includes(t)?t:e;a.style.colorScheme=n}null==o||o()},[]),O=o(e=>{k(e);try{localStorage.setItem(d,e)}catch(e){}},[t]),M=o(e=>{const n=p(e);L(n),"system"===E&&l&&!t&&I("system")},[E,t]);a(()=>{const e=window.matchMedia(i);return e.addListener(M),M(e),()=>e.removeListener(M)},[M]),a(()=>{const e=e=>{e.key===d&&O(e.newValue||y)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[O]),a(()=>{I(null!=t?t:E)},[t,E]);const A=s(()=>({theme:E,setTheme:O,forcedTheme:t,resolvedTheme:"system"===E?C:E,themes:l?[...h,"system"]:h,systemTheme:l?C:void 0}),[E,O,t,C,l,h]);/*#__PURE__*/return e.createElement(u.Provider,{value:A},/*#__PURE__*/e.createElement(g,{forcedTheme:t,disableTransitionOnChange:n,enableSystem:l,enableColorScheme:m,storageKey:d,themes:h,defaultTheme:y,attribute:$,value:f,children:w,attrs:x,nonce:T}),w)},g=/*#__PURE__*/l(({forcedTheme:t,storageKey:n,attribute:r,enableSystem:o,enableColorScheme:a,defaultTheme:s,value:l,attrs:m,nonce:d})=>{const u="system"===s,h="class"===r?`var d=document.documentElement,c=d.classList;c.remove(${m.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${r}',s='setAttribute';`,y=a?c.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",$=(e,t=!1,n=!0)=>{const o=l?l[e]:e,s=t?e+"|| ''":`'${o}'`;let m="";return a&&n&&!t&&c.includes(e)&&(m+=`d.style.colorScheme = '${e}';`),"class"===r?m+=t||o?`c.add(${s})`:"null":o&&(m+=`d[s](n,${s})`),m},v=t?`!function(){${h}${$(t)}}()`:o?`!function(){try{${h}var e=localStorage.getItem('${n}');if('system'===e||(!e&&${u})){var t='${i}',m=window.matchMedia(t);if(m.media!==t||m.matches){${$("dark")}}else{${$("light")}}}else if(e){${l?`var x=${JSON.stringify(l)};`:""}${$(l?"x[e]":"e",!0)}}${u?"":"else{"+$(s,!1,!1)+"}"}${y}}catch(e){}}()`:`!function(){try{${h}var e=localStorage.getItem('${n}');if(e){${l?`var x=${JSON.stringify(l)};`:""}${$(l?"x[e]":"e",!0)}}else{${$(s,!1,!1)};}${y}}catch(t){}}();`;/*#__PURE__*/return e.createElement("script",{nonce:d,dangerouslySetInnerHTML:{__html:v}})},()=>!0),S=(e,t)=>{if(d)return;let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},b=()=>{const e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},p=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light");export{$ as ThemeProvider,y as useTheme};
