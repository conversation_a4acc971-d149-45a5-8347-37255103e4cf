{"author": "<PERSON>", "description": "An Apollo Server integration for use with Next.js", "devDependencies": {"@apollo/server": "4.11.0", "@apollo/server-integration-testsuite": "4.11.0", "@babel/core": "7.26.0", "@changesets/changelog-github": "0.5.0", "@changesets/cli": "2.27.9", "@types/jest": "29.5.14", "@types/node": "18.19.59", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "babel-plugin-tsconfig-paths-module-resolver": "1.0.4", "eslint": "8.57.1", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.3", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "jest": "29.7.0", "next": "15.0.3", "next12": "npm:next@12.3.4", "next13": "npm:next@13.5.7", "next14": "npm:next@14.2.16", "next15": "npm:next@15.0.3", "prettier": "3.3.3", "typescript": "5.6.3", "wireit": "0.14.9"}, "engines": {"node": ">=18"}, "files": ["dist/**/*"], "license": "MIT", "main": "dist/index.js", "name": "@as-integrations/next", "peerDependencies": {"@apollo/server": "^4.0.0", "next": "^12.0.0 || ^13.0.0 || ^14.0.0 || ^15.0.0"}, "repository": "https://github.com/apollo-server-integrations/apollo-server-integration-next", "scripts": {"build": "wireit", "check": "wireit", "check:changeset": "bash -c 'AUTHOR=$(git show -s --format=\"%ae\" HEAD); if [[ $AUTHOR != *\"renovate\"* ]] && [[ $AUTHOR != *\"github-actions\"* ]]; then changeset status --since=origin/main; fi'", "check:eslint": "wireit", "check:prettier": "wireit", "check:tsc": "wireit", "fix": "wireit", "fix:eslint": "wireit", "fix:prettier": "wireit", "prepack": "npm run build", "publish:changeset": "changeset publish", "test": "wireit", "test:next": "DEBUG_PRINT_LIMIT=********** __NEXT_TEST_MODE=jest jest", "test:next12": "NEXT_VERSION=12 npm run test:next", "test:next13": "NEXT_VERSION=13 npm run test:next", "test:next14": "NEXT_VERSION=14 npm run test:next", "test:next15": "NEXT_VERSION=15 npm run test:next"}, "types": "dist/index.d.ts", "version": "3.2.0", "wireit": {"build": {"command": "tsc -p tsconfig.build.json", "dependencies": ["check:eslint", "check:prettier"], "output": ["./dist/**/*"]}, "check": {"dependencies": ["check:changeset", "check:eslint", "check:prettier", "check:tsc"]}, "check:eslint": {"command": "eslint --ignore-path .prettierignore $(git diff --diff-filter d --name-only HEAD~1 | grep -E '\\.(js|ts)$' | xargs)"}, "check:prettier": {"command": "prettier --check $(git diff --diff-filter d --name-only HEAD~1 | xargs)"}, "check:tsc": {"command": "tsc --noEmit"}, "fix": {"dependencies": ["fix:es<PERSON>", "fix:prettier"]}, "fix:eslint": {"command": "eslint --fix --ignore-path .prettierignore $(git diff --diff-filter d --name-only HEAD~1 | grep -E '\\.(js|ts)$' | xargs)"}, "fix:prettier": {"command": "prettier --write $(git diff --diff-filter d --name-only HEAD~1 | xargs)"}, "test": {"dependencies": ["test:next12", "test:next13", "test:next14", "test:next15"]}}}