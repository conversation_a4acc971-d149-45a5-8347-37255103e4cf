export { ApolloNextAppProvider } from "./ApolloNextAppProvider";
export { NextSSRInMemoryCache } from "./NextSSRInMemoryCache";
export { NextSSRApolloClient } from "./NextSSRApolloClient";
export { useFragment, useQuery, useSuspenseQuery, useReadQuery, useBackgroundQuery, } from "./hooks";
export { SSRMultipartLink } from "./SSRMultipartLink";
export { AccumulateMultipartResponsesLink as DebounceMultipartResponsesLink } from "./AccumulateMultipartResponsesLink";
export { RemoveMultipartDirectivesLink } from "./RemoveMultipartDirectivesLink";
export { resetNextSSRApolloSingletons } from "./testHelpers";
//# sourceMappingURL=index.d.ts.map