{"version": 3, "file": "NextSSRApolloClient.js", "sourceRoot": "", "sources": ["../../src/ssr/NextSSRApolloClient.tsx"], "names": [], "mappings": ";;;;;;AAOA,2CAA0D;AAE1D,wDAAiD;AACjD,gDAA0D;AAE1D,mEAAwE;AACxE,qEAGkC;AAClC,gEAAqC;AAErC,SAAS,eAAe,CAAc,MAA6B;IAajE,OAAO,MAAM,CAAC,cAAc,CAAC,CAAC;AAChC,CAAC;AAQD,MAAa,mBAEX,SAAQ,qBAAyB;IAQjC,YAAY,OAAyC;QACnD,KAAK,CAAC,OAAO,CAAC,CAAC;QART,uBAAkB,GAGtB;YACF,yBAAyB,EAAE,EAAE;SAC9B,CAAC;QAQM,8BAAyB,GAAG,IAAI,GAAG,EAA8B,CAAC;QAHxE,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAIO,mBAAmB,CAAC,OAG3B;QACC,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAClE,OAAO,CAAC,KAAK,CACd,CAAC;QACF,MAAM,YAAY,GAAG,eAAe,CAAc,IAAI,CAAC,CAAC;QACxD,0EAA0E;QAC1E,mDAAmD;QACnD,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAE1E,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAA,0BAAkB,EAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;QAEvE,MAAM,QAAQ,GAAG,CAAC,IAAA,iBAAK,EAAC,WAAW,CAAC,EAAE,kBAAkB,CAAC,CAAC,QAAQ,EAAE,CAAC;QAErE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IACvE,CAAC;IAEO,kBAAkB;QACxB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,uDAA8B,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;gBAChE,IAAA,qDAA6B,EAC3B,uDAA8B,EAC9B,CAAC,OAAO,EAAE,EAAE;;oBACV,iFAAiF;oBACjF,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU;wBAAE,OAAO;oBAE/C,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,GAChC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBAEpC,IAAI,CAAC,KAAK;wBAAE,OAAO;oBACnB,MAAM,kBAAkB,GAAG,IAAA,iBAAK,EAAC,KAAK,CAAC,CAAC;oBACxC,MAAM,YAAY,GAAG,eAAe,CAAc,IAAI,CAAC,CAAC;oBAExD,IAAI,eAAwB,CAAC;oBAC7B,IAAI,WAAsC,CAAC;oBAE3C,IAAI,QAAQ,IAAI,YAAY,CAAC,yBAAyB,CAAC,EAAE,CAAC;wBACxD,eAAe,GAAG,CAAC,CAAC,CAAA,MAAA,YAAY,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAC9D,kBAAkB,EAClB,OAAO,CACR,0CAAE,UAAU,CAAA,CAAC;oBAChB,CAAC;yBAAM,CAAC;wBACN,WAAW;4BACT,YAAY,CAAC,yBAAyB,CAAC,CAAC,GAAG,CACzC,kBAAkB,CACnB,IAAI,IAAI,GAAG,EAAE,CAAC;wBAEjB,YAAY,CAAC,yBAAyB,CAAC,CAAC,GAAG,CACzC,kBAAkB,EAClB,WAAW,CACZ,CAAC;wBAEF,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAC7C,CAAC;oBAED,IAAI,CAAC,eAAe,EAAE,CAAC;wBACrB,IAAI,uBAA2C;wBAC7C,wCAAwC;wBACxC,UAAmC,EACnC,aAAwC,CAAC;wBAE3C,MAAM,OAAO,GAAG,GAAG,EAAE;4BACnB,IACE,YAAY,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,aAAa;gCAE9D,YAAY,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;4BAElD,IAAI,WAAW,EAAE,CAAC;gCAChB,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,UAAU;oCACzC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;4BAChC,CAAC;iCAAM,IACL,QAAQ,IAAI,YAAY,CAAC,yBAAyB,CAAC,EACnD,CAAC;gCACD,YAAY,CAAC,yBAAyB,CAAC,CAAC,MAAM,CAC5C,kBAAkB,EAClB,OAAO,CACR,CAAC;4BACJ,CAAC;iCAAM,CAAC;gCACN,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;4BACtD,CAAC;4BAED,IACE,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC;gCAC5C,uBAAuB;gCAEvB,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBACpD,CAAC,CAAC;wBAEF,MAAM,OAAO,GAAG,IAAI,OAAO,CAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;4BAC3D,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAChC,QAAQ,EACR,CAAC,uBAAuB,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CACzD,CAAC;wBACJ,CAAC,CAAC,CAAC;wBAEH,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBAEzB,UAAU,GAAG,IAAI,mBAAU,CAAc,CAAC,QAAQ,EAAE,EAAE;4BACpD,OAAO;iCACJ,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gCACf,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gCACtB,QAAQ,CAAC,QAAQ,EAAE,CAAC;4BACtB,CAAC,CAAC;iCACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gCACb,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BACtB,CAAC,CAAC,CAAC;wBACP,CAAC,CAAC,CAAC;wBACH,IAAI,WAAW,EAAE,CAAC;4BAChB,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;wBACvC,CAAC;6BAAM,IAAI,QAAQ,IAAI,YAAY,CAAC,yBAAyB,CAAC,EAAE,CAAC;4BAC/D,YAAY,CAAC,yBAAyB,CAAC,CAAC,MAAM,CAC5C,kBAAkB,EAClB,OAAO,CACR,CAAC,UAAU,GAAG,UAAU,CAAC;wBAC5B,CAAC;6BAAM,CAAC;4BACN,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;wBACtD,CAAC;wBAED,YAAY,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAChC,QAAQ,EACR,CAAC,aAAa,GAAG,CAAC,MAAe,EAAE,EAAE;;4BACnC,MAAM,EAAE,MAAM,EAAE,GACd,MAAA,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,mCAAI,EAAE,CAAC;4BACrD,IAAI,MAAM,EAAE,CAAC;gCACX,MAAM,CAAC,MAAM,CAAC,CAAC;4BACjB,CAAC;4BACD,OAAO,EAAE,CAAC;wBACZ,CAAC,CAAC,CACH,CAAC;oBACJ,CAAC;gBACH,CAAC,CACF,CAAC;gBACF,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;oBACvC,MAAM,qBAAqB,GAAG,GAAG,EAAE;wBACjC,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;wBAC3C,sEAAsE;wBACtE,oFAAoF;wBACpF,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI;6BACrC,yBAAyB,EAAE,CAAC;4BAC7B,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;4BAChD,sBAAS,CAAC,KAAK,CACb,wFAAwF,EACxF,SAAS,CAAC,OAAO,CAClB,CAAC;4BACF,MAAM,OAAO,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;4BAC/C,YAAY;iCACT,UAAU,CAAC,OAAO,kCACd,SAAS,CAAC,OAAO,KACpB,OAAO,kCACF,SAAS,CAAC,OAAO,CAAC,OAAO,KAC5B,kBAAkB,EAAE,KAAK,OAE3B;iCACD,OAAO,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;iCAC9C,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;wBAC/C,CAAC;oBACH,CAAC,CAAC;oBACF,uEAAuE;oBACvE,iFAAiF;oBACjF,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,qBAAqB,EAAE;wBACrD,IAAI,EAAE,IAAI;qBACX,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,0CAAiB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;gBACnD,IAAA,qDAA6B,EAAC,0CAAiB,EAAE,CAAC,IAAI,EAAE,EAAE;;oBACxD,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;oBACpD,MAAM,EAAE,OAAO,EAAE,GACf,MAAA,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,mCAAI,EAAE,CAAC;oBAErD,IAAI,OAAO,EAAE,CAAC;wBACZ,OAAO,CAAC;4BACN,IAAI,EAAE,IAAI,CAAC,MAAM;yBAClB,CAAC,CAAC;oBACL,CAAC;oBACD,kEAAkE;oBAClE,0DAA0D;oBAC1D,sBAAsB;oBACtB,sJAAsJ;oBACtJ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,UAAU,CAGR,OAAyC;QACzC,IAAI,OAAO,MAAM,IAAI,WAAW,EAAE,CAAC;YACjC,IACE,OAAO,CAAC,WAAW,KAAK,YAAY;gBACpC,OAAO,CAAC,WAAW,KAAK,SAAS,EACjC,CAAC;gBACD,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QACD,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,qBAAqB,CAAC,kBAA2C;QAC/D,IACE,kBAAkB,CAAC,yBAAyB;YAC5C,IAAI,CAAC,kBAAkB,CAAC,yBAAyB;YAEjD,kBAAkB,CAAC,yBAAyB,CAAC,IAAI,CAC/C,GAAG,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,CAC/D,CAAC;QACJ,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC/C,CAAC;CACF;AA5OD,kDA4OC"}