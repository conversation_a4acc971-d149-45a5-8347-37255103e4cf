'use client'
import React, { useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
import { useSearchParams } from 'next/navigation'
import {
    displayDescription,
    composeField,
    displayField,
    getFilteredFields,
    getSortOrder,
    getFieldLabel,
} from '@/app/ui/daily-checks/actions'

import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { useDebounceFn, useMediaQuery } from '@reactuses/core'
import <PERSON><PERSON><PERSON><PERSON> from '../crew-checker/crew-checker'
import dayjs from 'dayjs'
import { Label } from '@/components/ui/label'
import { DailyCheckField } from '@/components/daily-check-field'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import {
    AlertDialogNew,
    Sheet,
    SheetBody,
    SheetContent,
    SheetHeader,
    SheetTitle,
    Textarea,
} from '@/components/ui'
import { ReadSectionMemberComments } from './queries'

export default function Sail({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    setTab,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages: any
    refreshImages: any
}) {
    // Router removed as it's no longer needed with ActionFooter
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const tab = searchParams.get('tab') ?? ''
    const [saving, setSaving] = useState(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [sectionComment, setSectionComment] = useState('')
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [sail, setSail] = useState<Boolean>(vesselDailyCheck?.sail === 'Ok')
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const [additionalComment, setAdditionalComment] = useState<Boolean>(false)
    const commentModel = new SectionMemberCommentModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    const isWide = useMediaQuery('(min-width: 640px)')
    const [sailCrewResponsible, setSailCrewResponsible] = useState<any>(
        vesselDailyCheck?.sailCrewResponsible?.nodes?.map((member: any) => ({
            label: member.firstName + ' ' + member.surname,
            value: member.id,
        })),
    )

    // useEffect(() => {
    //     getSectionVesselDailyCheck_LogBookEntrySection({
    //         variables: {
    //             id: [vesselDailyCheck.id],
    //         },
    //     })
    // }, [tab])

    const [sailCheckTime, setSailCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.sailCheckTime ?? new Date()),
    )
    const handleSail = async (check: Boolean) => {
        setSail(check)
        if (+vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck.id,
                sail: check ? 'Ok' : 'Not_Ok',
            }
            if (offline) {
                const data = await dailyCheckModel.save(variables)
                setVesselDailyCheck([data])
                setSaving(true)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        sail: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onCompleted: () => {
                setSaving(true)
            },
            onError: (error) => {
                console.error('Error completing safety check', error)
            },
        },
    )

    // const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
    //     VesselDailyCheck_LogBookEntrySection,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data =
    //                 response.readVesselDailyCheck_LogBookEntrySections.nodes
    //             setVesselDailyCheck(data)
    //             setSaving(true)
    //         },
    //         onError: (error: any) => {
    //             console.error(
    //                 'VesselDailyCheck_LogBookEntrySection error',
    //                 error,
    //             )
    //         },
    //     },
    // )

    const handleSetTab = (tab: any) => {
        setTab(tab)
    }

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        ReadSectionMemberComments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const fields = [
        {
            name: 'Sail',
            label: 'Sail',
            value: 'sail',
            // currentValue: vesselDailyCheck?.sail,
            checked: vesselDailyCheck?.sail,
            sortOrder: getSortOrder('Sail', logBookConfig),
            handleChange: handleSail,
        },
    ]

    const handleSave = async () => {
        // if (+vesselDailyCheck.id > 0) {
        //     if (offline) {
        //         const data = await dailyCheckModel.getByIds([
        //             vesselDailyCheck.id,
        //         ])
        //         setVesselDailyCheck(data)
        //         setSaving(true)
        //     } else {
        //         getSectionVesselDailyCheck_LogBookEntrySection({
        //             variables: {
        //                 id: [vesselDailyCheck.id],
        //             },
        //         })
        //     }
        // }

        const comment = sectionComment
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: 'Sail',
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const handleSailCrewResponsible = async (crews: any) => {
        setSailCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                sailCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleSailCheckTime = async (date: any) => {
        setSailCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                sailCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('offline')
                /* const newVesselDailyCheck =
                                            await dailyCheckModel.save(variables)
                                        // setSaving(true)
                                        setSaving(false)
                                        setVesselDailyCheck([newVesselDailyCheck])
                                        const sections = logbook.logBookEntrySections.nodes
                                        const section = {
                                            className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                                            id: `${vesselDailyCheck.id}`,
                                            logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                                            __typename: 'VesselDailyCheck_LogBookEntrySection',
                                        }
                                        if (
                                            !sections.some(
                                                (s: any) =>
                                                    JSON.stringify(s) === JSON.stringify(section),
                                            )
                                        ) {
                                            sections.push(section)
                                        }
                                        const lb = {
                                            ...logbook,
                                            logBookEntrySections: { nodes: sections },
                                        }
                                        await logBookModel.save(lb)
                                        getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        sailCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    // useEffect(() => {
    //     getSectionVesselDailyCheck_LogBookEntrySection({
    //         variables: {
    //             id: [vesselDailyCheck.id],
    //         },
    //     })
    // }, [])

    const saveSectionComment = () => {
        getComment('Sail', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('Sail', 'Section')?.id,
                          comment: sectionComment,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'Sail',
                          comment: sectionComment,
                          logBookEntryID: +logentryID,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)

    const saveCheckTime = () => {
        if (vesselDailyCheck?.id > 0) {
            updateVesselDailyCheck_LogBookEntrySection({
                variables: {
                    input: {
                        id: vesselDailyCheck.id,
                        sailCheckTime:
                            dayjs(sailCheckTime).format('YYYY-MM-DD HH:mm'),
                    },
                },
            })
        }
    }
    const { run: debounceSaveCheckTime } = useDebounceFn(() => {
        saveCheckTime()
    }, 1000)

    return (
        <>
            <div className=" p-6 border   rounded-lg ">
                {/* <div className="flex w-full justify-between items-center">
                    <div className="text-2xs uppercase font-inter w-48 text-left">
                        Sail
                    </div>
                </div> */}
                <div className="grid grid-cols-1 my-3 md:grid-cols-2 lg:grid-cols-3 items-start">
                    <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                        {logBookConfig && vesselDailyCheck && (
                            <>
                                {getFilteredFields(
                                    fields,
                                    false,
                                    logBookConfig,
                                ).map((field: any, index: number) => (
                                    <DailyCheckField
                                        locked={locked || !edit_logBookEntry}
                                        key={index}
                                        displayField={displayField(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        displayDescription={displayDescription(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        setDescriptionPanelContent={(content) =>
                                            setDescriptionPanelContent(
                                                content as string,
                                            )
                                        }
                                        setOpenDescriptionPanel={
                                            setOpenDescriptionPanel
                                        }
                                        setDescriptionPanelHeading={
                                            setDescriptionPanelHeading
                                        }
                                        displayLabel={getFieldLabel(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        inputId={field.value}
                                        handleNoChange={() =>
                                            field.handleChange(false)
                                        }
                                        defaultNoChecked={
                                            field.checked === 'Not_Ok'
                                        }
                                        handleYesChange={() =>
                                            field.handleChange(true)
                                        }
                                        defaultYesChecked={
                                            field.checked === 'Ok'
                                        }
                                        commentAction={() =>
                                            showCommentPopup(
                                                getComment(field.name),
                                                composeField(
                                                    field.name,
                                                    logBookConfig,
                                                ),
                                            )
                                        }
                                        comment={
                                            getComment(field.name)?.comment
                                        }
                                        displayImage={true}
                                        fieldImages={fieldImages}
                                        onImageUpload={refreshImages}
                                    />
                                ))}
                                {getFilteredFields(fields, true, logBookConfig)
                                    ?.filter((groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    ?.map((groupField: any) => (
                                        <div
                                            key={groupField.name}
                                            className="border rounded-lg p-4 col-span-3 my-4">
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                                                <div className=" my-3 mr-4">
                                                    {groupField.field?.title
                                                        ? groupField.field.title
                                                        : groupField.field
                                                              .label}
                                                    {displayDescription(
                                                        groupField.name,
                                                        logBookConfig,
                                                    ) && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    displayDescription(
                                                                        groupField.name,
                                                                        logBookConfig,
                                                                    ),
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    groupField.name,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                                <div className="md:col-span-2">
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    field.handleChange(
                                                                        false,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    field.handleChange(
                                                                        true,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                                displayImage={
                                                                    true
                                                                }
                                                                fieldImages={
                                                                    fieldImages
                                                                }
                                                                onImageUpload={
                                                                    refreshImages
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </>
                        )}
                    </div>
                </div>

                <CrewChecker
                    vesselDailyCheckID={vesselDailyCheck.id}
                    crewKey="SailCrewResponsible"
                    timeKey="SailCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handleSailCrewResponsible}
                    crewResponsible={sailCrewResponsible}
                    checkTime={sailCheckTime}
                    handleCheckTime={handleSailCheckTime}
                    // setCheckTime={setSailCheckTime}
                    setCheckTime={(e) => {
                        setSailCheckTime(e)
                        debounceSaveCheckTime()
                    }}
                />

                <Label htmlFor="section_comment" label="Comments">
                    <Textarea
                        id="section_comment"
                        disabled={locked || !edit_logBookEntry}
                        rows={4}
                        placeholder="Comments ..."
                        onChange={(e) => {
                            setSectionComment(e.target.value)
                            debounceSaveSectionComment()
                        }}
                        defaultValue={getComment('Sail', 'Section')?.comment}
                    />
                </Label>
            </div>
            {/* <ActionFooter
                showFooter={!locked || edit_logBookEntry}
                onCreateTask={handleCreateTask}
                onSave={handleSave}
                createTaskLoading={createMaintenanceCheckLoading}
                createTaskDisabled={createMaintenanceCheckLoading}
            /> */}
            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                actionText="Save">
                <div
                    className={`flex flex-col ${locked || !edit_logBookEntry ? 'pointer-events-none' : ''}`}>
                    <Label className=" font-medium  ">Comment</Label>
                    <textarea
                        readOnly={locked || !edit_logBookEntry}
                        id="comment"
                        rows={4}
                        className="block p-2.5 w-full mt-4    rounded-lg border          "
                        placeholder="Comment"
                        defaultValue={
                            currentComment ? currentComment.comment : ''
                        }></textarea>
                </div>
            </AlertDialogNew>
            <Sheet
                open={openDescriptionPanel}
                onOpenChange={setOpenDescriptionPanel}>
                <SheetContent>
                    <SheetHeader>
                        <SheetTitle>
                            Field -{' '}
                            <span className="font-thin">
                                {descriptionPanelHeading}
                            </span>
                        </SheetTitle>
                    </SheetHeader>
                    <SheetBody>
                        <div
                            className="leading-loose "
                            dangerouslySetInnerHTML={{
                                __html: descriptionPanelContent,
                            }}></div>
                    </SheetBody>
                </SheetContent>
            </Sheet>
        </>
    )
}
