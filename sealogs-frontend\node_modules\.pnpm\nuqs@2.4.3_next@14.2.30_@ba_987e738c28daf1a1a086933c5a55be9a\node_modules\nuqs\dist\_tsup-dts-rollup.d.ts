import { AdapterContext as AdapterContext_2 } from './context';
import { AdapterInterface as AdapterInterface_2 } from './defs';
import { Context } from 'react';
import { DetailedReactHTMLElement } from 'react';
import { Emitter } from 'mitt';
import { FunctionComponentElement } from 'react';
import { InputHTMLAttributes } from 'react';
import { ProviderProps } from 'react';
import { ReactNode } from 'react';
import type { TransitionStartFunction } from 'react';
import { unstable_AdapterContext as unstable_AdapterContext_2 } from './custom';
import { unstable_AdapterContext as unstable_AdapterContext_3 } from '../custom';

declare type AdapterContext = {
    useAdapter: UseAdapterHook;
};
export { AdapterContext }
export { AdapterContext as unstable_AdapterContext }

declare type AdapterInterface = {
    searchParams: URLSearchParams;
    updateUrl: UpdateUrlFunction;
    getSearchParamsSnapshot?: () => URLSearchParams;
    rateLimitFactor?: number;
};
export { AdapterInterface }
export { AdapterInterface as unstable_AdapterInterface }

declare type AdapterOptions = Pick<Options, 'history' | 'scroll' | 'shallow'>;
export { AdapterOptions }
export { AdapterOptions as unstable_AdapterOptions }

declare type Base = string | URLSearchParams | URL;

export declare function compareSearchParams(a: SearchParams, b: SearchParams): boolean;

export declare function compose(fns: React.TransitionStartFunction[], final: () => void): void;

export declare const context: Context<AdapterContext>;

/**
 * Create a custom adapter (context provider) for nuqs to work with your framework / router.
 *
 * Adapters are based on React Context,
 *
 * @param useAdapter
 * @returns
 */
declare function createAdapterProvider(useAdapter: UseAdapterHook): ({ children, ...props }: {
    children: ReactNode;
}) => FunctionComponentElement<ProviderProps<AdapterContext>>;
export { createAdapterProvider }
export { createAdapterProvider as unstable_createAdapterProvider }

declare function createLoader<Parsers extends ParserMap>(parsers: Parsers, { urlKeys }?: LoaderOptions<Parsers>): {
    (input: LoaderInput, options?: LoaderOptions<Parsers>): inferParserType<Parsers>;
    (input: Promise<LoaderInput>, options?: LoaderOptions<Parsers>): Promise<inferParserType<Parsers>>;
};
export { createLoader }
export { createLoader as createLoader_alias_1 }
export { createLoader as createLoader_alias_2 }

/**
 * Wrap a set of parse/serialize functions into a builder pattern parser
 * you can pass to one of the hooks, making its default value type safe.
 */
declare function createParser<T>(parser: Require<Parser<T>, 'parse' | 'serialize'>): ParserBuilder<T>;
export { createParser }
export { createParser as createParser_alias_1 }
export { createParser as createParser_alias_2 }

export declare function createReactRouterBasedAdapter({ adapter, useNavigate, useSearchParams }: CreateReactRouterBasedAdapterArgs): {
    NuqsAdapter: ({ children, ...props }: {
        children: ReactNode;
    }) => FunctionComponentElement<ProviderProps<AdapterContext_2>>;
    useOptimisticSearchParams: () => URLSearchParams;
};

declare type CreateReactRouterBasedAdapterArgs = {
    adapter: string;
    useNavigate: UseNavigate;
    useSearchParams: UseSearchParams;
};

declare function createSearchParamsCache<Parsers extends ParserMap>(parsers: Parsers, { urlKeys }?: {
    urlKeys?: UrlKeys<Parsers>;
}): {
    parse: {
        (searchParams: SearchParams): inferParserType<Parsers>;
        (searchParams: Promise<any>): Promise<inferParserType<Parsers>>;
    };
    get: <Key extends keyof Parsers>(key: Key) => inferParserType<Parsers>[Key];
    all: () => inferParserType<Parsers>;
};
export { createSearchParamsCache }
export { createSearchParamsCache as createSearchParamsCache_alias_1 }

declare function createSerializer<Parsers extends ParserMap>(parsers: Parsers, { clearOnDefault, urlKeys }?: Pick<Options, 'clearOnDefault'> & {
    urlKeys?: UrlKeys<Parsers>;
}): {
    (values: Partial<inferParserType<Parsers> extends infer T ? { [K in keyof T]: inferParserType<Parsers>[K] | null; } : never>): string;
    (base: Base, values: Partial<inferParserType<Parsers> extends infer T ? { [K in keyof T]: inferParserType<Parsers>[K] | null; } : never> | null): string;
};
export { createSerializer }
export { createSerializer as createSerializer_alias_1 }
export { createSerializer as createSerializer_alias_2 }

export declare type CrossHookSyncPayload = {
    state: any;
    query: string | null;
};

export declare function debug(message: string, ...args: any[]): void;

export declare const debugEnabled: boolean;

export declare const emitter: Emitter<EventMap>;

/**
 * Opt-in to syncing shallow updates of the URL with the useOptimisticSearchParams hook.
 *
 * By default, the useOptimisticSearchParams hook will only react to internal nuqs updates.
 * If third party code updates the History API directly, use this function to
 * enable useOptimisticSearchParams to react to those changes.
 */
export declare function enableHistorySync(): void;

export declare function encodeQueryValue(input: string): string;

export declare function enqueueQueryStringUpdate<Value>(key: string, value: Value | null, serialize: (value: Value) => string, options: Pick<Options, 'history' | 'scroll' | 'shallow' | 'startTransition' | 'throttleMs'>): string | null;

export declare function error(code: keyof typeof errors): string;

export declare const errors: {
    readonly 303: "Multiple adapter contexts detected. This might happen in monorepos.";
    readonly 404: "nuqs requires an adapter to work with your framework.";
    readonly 409: "Multiple versions of the library are loaded. This may lead to unexpected behavior. Currently using `%s`, but `%s` (via the %s adapter) was about to load on top.";
    readonly 414: "Max safe URL length exceeded. Some browsers may not be able to accept this URL. Consider limiting the amount of state stored in the URL.";
    readonly 429: "URL update rate-limited by the browser. Consider increasing `throttleMs` for key(s) `%s`. %O";
    readonly 500: "Empty search params cache. Search params can't be accessed in Layouts.";
    readonly 501: "Search params cache already populated. Have you called `parse` twice?";
};

declare type EventMap = {
    [key: string]: CrossHookSyncPayload;
};

/**
 * Next.js pages router merges dynamic URL params with search params in its
 * internal state.
 * However, we need to pass just the URL params to the href part of the router
 * update functions.
 * This function finds the dynamic URL params placeholders in the pathname
 * (eg: `/path/[foo]/[bar]`) and extracts the corresponding values from the
 * query state object, leaving out any other search params.
 */
export declare function extractDynamicUrlParams(pathname: string, values: Record<string, string | string[] | undefined>): Record<string, string | string[] | undefined>;

export declare const FLUSH_RATE_LIMIT_MS: number;

export declare function getAsPathPathname(asPath: string): string;

export declare function getDefaultThrottle(): 50 | 120 | 320;

export declare function getQueuedValue(key: string): string | null | undefined;

export declare function getSearchParams(url: string | URL): URLSearchParams;

declare type HistoryOptions = 'replace' | 'push';
export { HistoryOptions }
export { HistoryOptions as HistoryOptions_alias_1 }
export { HistoryOptions as HistoryOptions_alias_2 }

export declare const historyUpdateMarker = "__nuqs__";

declare type inferParserRecordType<Map extends Record<string, ParserBuilder<any>>> = {
    [Key in keyof Map]: inferSingleParserType<Map[Key]>;
} & {};

/**
 * Type helper to extract the underlying returned data type of a parser
 * or of an object describing multiple parsers and their associated keys.
 *
 * Usage:
 *
 * ```ts
 * import { type inferParserType } from 'nuqs' // or 'nuqs/server'
 *
 * const intNullable = parseAsInteger
 * const intNonNull = parseAsInteger.withDefault(0)
 *
 * inferParserType<typeof intNullable> // number | null
 * inferParserType<typeof intNonNull> // number
 *
 * const parsers = {
 *  a: parseAsInteger,
 *  b: parseAsBoolean.withDefault(false)
 * }
 *
 * inferParserType<typeof parsers>
 * // { a: number | null, b: boolean }
 * ```
 */
declare type inferParserType<Input> = Input extends ParserBuilder<any> ? inferSingleParserType<Input> : Input extends Record<string, ParserBuilder<any>> ? inferParserRecordType<Input> : never;
export { inferParserType }
export { inferParserType as inferParserType_alias_1 }
export { inferParserType as inferParserType_alias_2 }

declare type inferSingleParserType<Parser> = Parser extends ParserBuilder<infer Value> & {
    defaultValue: infer Value;
} ? Value : Parser extends ParserBuilder<infer Value> ? Value | null : never;

export declare function isPagesRouter(): boolean;

/**
 * Test that a parser is bijective (serialize then parse gives back the same value).
 *
 * It will throw if the parser does not serialize the input to the expected serialized value,
 * or if the parser does not parse the serialized value to the expected input value.
 * The parser's `eq` function (if provided, otherwise `===`) is used to compare the values.
 *
 * Usage:
 * ```ts
 * // Expect it to pass (no error thrown)
 * expect(isParserBijective(parseAsInteger, '42', 42)).toBe(true)
 * // Expect it to fail
 * expect(() => isParserBijective(parseAsInteger, '42', 47)).toThrow()
 * ```
 *
 * @param parser The parser to test
 * @param serialized The serialized representation of the input to test against
 * @param input An input value to test against
 * @returns `true` if the test passes, otherwise it will throw.
 */
export declare function isParserBijective<T>(parser: ParserBuilder<T>, serialized: string, input: T): boolean;

declare type KeyMapValue<Type> = Parser<Type> & Options & {
    defaultValue?: Type;
};

declare type LoaderFunction<Parsers extends ParserMap> = ReturnType<typeof createLoader<Parsers>>;
export { LoaderFunction }
export { LoaderFunction as LoaderFunction_alias_1 }
export { LoaderFunction as LoaderFunction_alias_2 }

declare type LoaderInput = URL | Request | URLSearchParams | Record<string, string | string[] | undefined> | string;
export { LoaderInput }
export { LoaderInput as LoaderInput_alias_1 }
export { LoaderInput as LoaderInput_alias_2 }

declare type LoaderOptions<Parsers extends ParserMap> = {
    urlKeys?: UrlKeys<Parsers>;
};
export { LoaderOptions }
export { LoaderOptions as LoaderOptions_alias_1 }
export { LoaderOptions as LoaderOptions_alias_2 }

declare type NavigateFn = (url: NavigateUrl, options: NavigateOptions) => void;

declare type NavigateOptions = {
    replace?: boolean;
    preventScrollReset?: boolean;
    state?: unknown;
};

declare type NavigateUrl = {
    hash?: string;
    search?: string;
};

declare type Nullable<T> = {
    [K in keyof T]: T[K] | null;
} & {};
export { Nullable }
export { Nullable as Nullable_alias_1 }
export { Nullable as Nullable_alias_2 }

export declare const NuqsAdapter: ({ children, ...props }: {
    children: ReactNode;
}) => FunctionComponentElement<ProviderProps<unstable_AdapterContext_2>>;

declare const NuqsAdapter_2: ({ children, ...props }: {
    children: ReactNode;
}) => FunctionComponentElement<ProviderProps<unstable_AdapterContext_3>>;
export { NuqsAdapter_2 as NuqsAdapter_alias_1 }
export { NuqsAdapter_2 as NuqsAdapter_alias_7 }

export declare function NuqsAdapter_alias_2({ children, fullPageNavigationOnShallowFalseUpdates }: {
    children: ReactNode;
    fullPageNavigationOnShallowFalseUpdates?: boolean;
}): FunctionComponentElement<ProviderProps<    {
fullPageNavigationOnShallowFalseUpdates: boolean;
}>>;

export declare const NuqsAdapter_alias_3: ({ children, ...props }: {
    children: ReactNode;
}) => FunctionComponentElement<ProviderProps<unstable_AdapterContext_2>>;

export declare const NuqsAdapter_alias_4: ({ children, ...props }: {
    children: ReactNode;
}) => FunctionComponentElement<ProviderProps<unstable_AdapterContext_3>>;

export declare const NuqsAdapter_alias_5: ({ children, ...props }: {
    children: ReactNode;
}) => FunctionComponentElement<ProviderProps<unstable_AdapterContext_3>>;

export declare const NuqsAdapter_alias_6: ({ children, ...props }: {
    children: ReactNode;
}) => FunctionComponentElement<ProviderProps<unstable_AdapterContext_3>>;

export declare const NuqsAdapter_alias_8: ({ children, ...props }: {
    children: ReactNode;
}) => FunctionComponentElement<ProviderProps<unstable_AdapterContext_3>>;

export declare function NuqsTestingAdapter({ resetUrlUpdateQueueOnMount, ...props }: TestingAdapterProps): FunctionComponentElement<ProviderProps<unstable_AdapterContext_2>>;

export declare type OnUrlUpdateFunction = (event: UrlUpdateEvent) => void;

declare type Options = {
    /**
     * How the query update affects page history
     *
     * `push` will create a new history entry, allowing to use the back/forward
     * buttons to navigate state updates.
     * `replace` (default) will keep the current history point and only replace
     * the query string.
     */
    history?: HistoryOptions;
    /**
     * Scroll to top after a query state update
     *
     * Defaults to `false`, unlike the Next.js router page navigation methods.
     */
    scroll?: boolean;
    /**
     * Shallow mode (true by default) keeps query states update client-side only,
     * meaning there won't be calls to the server.
     *
     * Setting it to `false` will trigger a network request to the server with
     * the updated querystring.
     */
    shallow?: boolean;
    /**
     * Maximum amount of time (ms) to wait between updates of the URL query string.
     *
     * This is to alleviate rate-limiting of the Web History API in browsers,
     * and defaults to 50ms. Safari requires a much higher value of around 340ms.
     *
     * Note: the value will be limited to a minimum of 50ms, anything lower
     * will not have any effect.
     */
    throttleMs?: number;
    /**
     * In RSC frameworks, opt-in to observing Server Component loading states when
     * doing non-shallow updates by passing a `startTransition` from the
     * `React.useTransition()` hook.
     *
     * In other frameworks, navigation events triggered by a query update can also
     * be wrapped in a transition this way (e.g. `React.startTransition`).
     */
    startTransition?: TransitionStartFunction;
    /**
     * Clear the key-value pair from the URL query string when setting the state
     * to the default value.
     *
     * Defaults to `true` to keep URLs clean.
     *
     * Set it to `false` to keep backwards-compatiblity when the default value
     * changes (prefer explicit URLs whose meaning don't change).
     */
    clearOnDefault?: boolean;
};
export { Options }
export { Options as Options_alias_1 }
export { Options as Options_alias_2 }

/**
 * A comma-separated list of items.
 * Items are URI-encoded for safety, so they may not look nice in the URL.
 *
 * @param itemParser Parser for each individual item in the array
 * @param separator The character to use to separate items (default ',')
 */
declare function parseAsArrayOf<ItemType>(itemParser: Parser<ItemType>, separator?: string): ParserBuilder<ItemType[]>;
export { parseAsArrayOf }
export { parseAsArrayOf as parseAsArrayOf_alias_1 }
export { parseAsArrayOf as parseAsArrayOf_alias_2 }

declare const parseAsBoolean: ParserBuilder<boolean>;
export { parseAsBoolean }
export { parseAsBoolean as parseAsBoolean_alias_1 }
export { parseAsBoolean as parseAsBoolean_alias_2 }

declare const parseAsFloat: ParserBuilder<number>;
export { parseAsFloat }
export { parseAsFloat as parseAsFloat_alias_1 }
export { parseAsFloat as parseAsFloat_alias_2 }

declare const parseAsHex: ParserBuilder<number>;
export { parseAsHex }
export { parseAsHex as parseAsHex_alias_1 }
export { parseAsHex as parseAsHex_alias_2 }

declare const parseAsIndex: ParserBuilder<number>;
export { parseAsIndex }
export { parseAsIndex as parseAsIndex_alias_1 }
export { parseAsIndex as parseAsIndex_alias_2 }

declare const parseAsInteger: ParserBuilder<number>;
export { parseAsInteger }
export { parseAsInteger as parseAsInteger_alias_1 }
export { parseAsInteger as parseAsInteger_alias_2 }

/**
 * Querystring encoded as an ISO-8601 string (UTC)
 * without the time zone offset, and returned as
 * a Date object.
 *
 * The Date is parsed without the time zone offset,
 * making it at 00:00:00 UTC.
 */
declare const parseAsIsoDate: ParserBuilder<Date>;
export { parseAsIsoDate }
export { parseAsIsoDate as parseAsIsoDate_alias_1 }
export { parseAsIsoDate as parseAsIsoDate_alias_2 }

/**
 * Querystring encoded as an ISO-8601 string (UTC),
 * and returned as a Date object.
 */
declare const parseAsIsoDateTime: ParserBuilder<Date>;
export { parseAsIsoDateTime }
export { parseAsIsoDateTime as parseAsIsoDateTime_alias_1 }
export { parseAsIsoDateTime as parseAsIsoDateTime_alias_2 }

/**
 * Encode any object shape into the querystring value as JSON.
 * Note: you may want to use `useQueryStates` for finer control over
 * multiple related query keys.
 *
 * @param runtimeParser Runtime parser (eg: Zod schema) to validate after JSON.parse
 */
declare function parseAsJson<T>(runtimeParser: (value: unknown) => T): ParserBuilder<T>;
export { parseAsJson }
export { parseAsJson as parseAsJson_alias_1 }
export { parseAsJson as parseAsJson_alias_2 }

/**
 * Number-based literals provide better type-safety for known sets of values.
 * You will need to pass the parseAsNumberLiteral function a list of your number values
 * in order to validate the query string. Anything else will return `null`,
 * or your default value if specified.
 *
 * Example:
 * ```ts
 * const diceSides = [1, 2, 3, 4, 5, 6] as const
 *
 * const [side, setSide] = useQueryState(
 *   'side',
 *    parseAsNumberLiteral(diceSides) // pass a readonly list of allowed values
 *      .withDefault(4)
 * )
 * ```
 *
 * @param validValues The values you want to accept
 */
declare function parseAsNumberLiteral<Literal extends number>(validValues: readonly Literal[]): ParserBuilder<Literal>;
export { parseAsNumberLiteral }
export { parseAsNumberLiteral as parseAsNumberLiteral_alias_1 }
export { parseAsNumberLiteral as parseAsNumberLiteral_alias_2 }

declare const parseAsString: ParserBuilder<string>;
export { parseAsString }
export { parseAsString as parseAsString_alias_1 }
export { parseAsString as parseAsString_alias_2 }

/**
 * String-based enums provide better type-safety for known sets of values.
 * You will need to pass the parseAsStringEnum function a list of your enum values
 * in order to validate the query string. Anything else will return `null`,
 * or your default value if specified.
 *
 * Example:
 * ```ts
 * enum Direction {
 *   up = 'UP',
 *   down = 'DOWN',
 *   left = 'LEFT',
 *   right = 'RIGHT'
 * }
 *
 * const [direction, setDirection] = useQueryState(
 *   'direction',
 *    parseAsStringEnum<Direction>(Object.values(Direction)) // pass a list of allowed values
 *      .withDefault(Direction.up)
 * )
 * ```
 *
 * Note: the query string value will be the value of the enum, not its name
 * (example above: `direction=UP`).
 *
 * @param validValues The values you want to accept
 */
declare function parseAsStringEnum<Enum extends string>(validValues: Enum[]): ParserBuilder<Enum>;
export { parseAsStringEnum }
export { parseAsStringEnum as parseAsStringEnum_alias_1 }
export { parseAsStringEnum as parseAsStringEnum_alias_2 }

/**
 * String-based literals provide better type-safety for known sets of values.
 * You will need to pass the parseAsStringLiteral function a list of your string values
 * in order to validate the query string. Anything else will return `null`,
 * or your default value if specified.
 *
 * Example:
 * ```ts
 * const colors = ["red", "green", "blue"] as const
 *
 * const [color, setColor] = useQueryState(
 *   'color',
 *    parseAsStringLiteral(colors) // pass a readonly list of allowed values
 *      .withDefault("red")
 * )
 * ```
 *
 * @param validValues The values you want to accept
 */
declare function parseAsStringLiteral<Literal extends string>(validValues: readonly Literal[]): ParserBuilder<Literal>;
export { parseAsStringLiteral }
export { parseAsStringLiteral as parseAsStringLiteral_alias_1 }
export { parseAsStringLiteral as parseAsStringLiteral_alias_2 }

/**
 * Querystring encoded as the number of milliseconds since epoch,
 * and returned as a Date object.
 */
declare const parseAsTimestamp: ParserBuilder<Date>;
export { parseAsTimestamp }
export { parseAsTimestamp as parseAsTimestamp_alias_1 }
export { parseAsTimestamp as parseAsTimestamp_alias_2 }

declare type Parser<T> = {
    /**
     * Convert a query string value into a state value.
     *
     * If the string value does not represent a valid state value,
     * the parser should return `null`. Throwing an error is also supported.
     */
    parse: (value: string) => T | null;
    /**
     * Render the state value into a query string value.
     */
    serialize?: (value: T) => string;
    /**
     * Check if two state values are equal.
     *
     * This is used when using the `clearOnDefault` value, to compare the default
     * value with the set value.
     *
     * It makes sense to provide this function when the state value is an object
     * or an array, as the default referential equality check will not work.
     */
    eq?: (a: T, b: T) => boolean;
};
export { Parser }
export { Parser as Parser_alias_1 }
export { Parser as Parser_alias_2 }

declare type ParserBuilder<T> = Required<Parser<T>> & Options & {
    /**
     * Set history type, shallow routing and scroll restoration options
     * at the hook declaration level.
     *
     * Note that you can override those options in individual calls to the
     * state updater function.
     */
    withOptions<This>(this: This, options: Options): This;
    /**
     * Specifying a default value makes the hook state non-nullable when the
     * query is missing from the URL: the default value is returned instead
     * of `null`.
     *
     * Setting the state to the default value¹ will clear the query string key
     * from the URL, unless `clearOnDefault` is set to `false`.
     *
     * Setting the state to `null` will always clear the query string key
     * from the URL, and return the default value.
     *
     * ¹: Equality is checked with the parser's `eq` function, or referential
     * equality if not provided.
     *
     * @param defaultValue
     */
    withDefault(this: ParserBuilder<T>, defaultValue: NonNullable<T>): Omit<ParserBuilder<T>, 'parseServerSide'> & {
        readonly defaultValue: NonNullable<T>;
        /**
         * Use the parser in Server Components
         *
         * `parse` is intended to be used only by the hook, but you can use this
         * method to hydrate query values on server-side rendered pages.
         * See the `server-side-parsing` demo for an example.
         *
         * Note that when multiple queries are presented to the parser
         * (eg: `/?a=1&a=2`), only the **first** will be parsed, to mimic the
         * behaviour of URLSearchParams:
         * https://url.spec.whatwg.org/#dom-urlsearchparams-get
         *
         * @param value as coming from page props
         */
        parseServerSide(value: string | string[] | undefined): NonNullable<T>;
    };
    /**
     * Use the parser in Server Components
     *
     * `parse` is intended to be used only by the hook, but you can use this
     * method to hydrate query values on server-side rendered pages.
     * See the `server-side-parsing` demo for an example.
     *
     * Note that when multiple queries are presented to the parser
     * (eg: `/?a=1&a=2`), only the **first** will be parsed, to mimic the
     * behaviour of URLSearchParams:
     * https://url.spec.whatwg.org/#dom-urlsearchparams-get
     *
     * @param value as coming from page props
     */
    parseServerSide(value: string | string[] | undefined): T | null;
};
export { ParserBuilder }
export { ParserBuilder as ParserBuilder_alias_1 }
export { ParserBuilder as ParserBuilder_alias_2 }

declare type ParserMap = Record<string, ParserWithOptionalDefault<any>>;
export { ParserMap }
export { ParserMap as ParserMap_alias_1 }
export { ParserMap as ParserMap_alias_2 }

declare type ParserWithOptionalDefault<T> = ParserBuilder<T> & {
    defaultValue?: T;
};
export { ParserWithOptionalDefault }
export { ParserWithOptionalDefault as ParserWithOptionalDefault_alias_1 }
export { ParserWithOptionalDefault as ParserWithOptionalDefault_alias_2 }

export declare function patchHistory(emitter: SearchParamsSyncEmitter, adapter: string): void;

declare function renderQueryString(search: URLSearchParams): string;
export { renderQueryString }
export { renderQueryString as renderQueryString_alias_1 }

declare type Require<T, Keys extends keyof T> = Pick<Required<T>, Keys> & Omit<T, Keys>;

export declare function resetQueue(): void;

export declare function safeParse<T>(parser: Parser<T>['parse'], value: string, key?: string): T | null;

/**
 * Eventually flush the update queue to the URL query string.
 *
 * This takes care of throttling to avoid hitting browsers limits
 * on calls to the history pushState/replaceState APIs, and defers
 * the call so that individual query state updates can be batched
 * when running in the same event loop tick.
 *
 * @returns a Promise to the URLSearchParams that have been applied.
 */
export declare function scheduleFlushToURL({ getSearchParamsSnapshot, updateUrl, rateLimitFactor }: Pick<AdapterInterface, 'updateUrl' | 'getSearchParamsSnapshot' | 'rateLimitFactor'>): Promise<URLSearchParams>;

declare type SearchParams = Record<string, string | string[] | undefined>;
export { SearchParams }
export { SearchParams as SearchParams_alias_1 }
export { SearchParams as SearchParams_alias_2 }

export declare type SearchParamsSyncEmitter = Emitter<{
    update: URLSearchParams;
}>;

declare type SetValues<T extends UseQueryStatesKeysMap> = (values: Partial<Nullable<Values<T>>> | UpdaterFn<T> | null, options?: Options) => Promise<URLSearchParams>;
export { SetValues }
export { SetValues as SetValues_alias_1 }

export declare function sprintf(base: string, ...args: any[]): string;

declare type TestingAdapterProps = {
    searchParams?: string | Record<string, string> | URLSearchParams;
    onUrlUpdate?: OnUrlUpdateFunction;
    rateLimitFactor?: number;
    resetUrlUpdateQueueOnMount?: boolean;
    children: ReactNode;
};

/**
 * Tests that a parser is bijective (parse then serialize gives back the same query string).
 *
 * It will throw if the parser is not bijective (if the serialized value is not equal to the input query).
 *
 * Usage:
 * ```ts
 * // Expect it to pass (no error thrown)
 * expect(testParseThenSerialize(myParser, 'foo')).toBe(true)
 * // Expect it to fail
 * expect(() => testParseThenSerialize(myParser, 'bar')).toThrow()
 * ```
 *
 * @param parser The parser to test
 * @param input A query string to test against
 * @returns `true` if the test passes, otherwise it will throw.
 */
export declare function testParseThenSerialize<T>(parser: ParserBuilder<T>, input: string): boolean;

/**
 * Test that a parser is bijective (serialize then parse gives back the same value).
 *
 * It will throw if the parser is not bijective (if the parsed value is not equal to the input value).
 * The parser's `eq` function is used to compare the values.
 *
 * Usage:
 * ```ts
 * // Expect it to pass (no error thrown)
 * expect(testSerializeThenParse(myParser, 'foo')).toBe(true)
 * // Expect it to fail
 * expect(() => testSerializeThenParse(myParser, 'bar')).toThrow()
 * ```
 *
 * @param parser The parser to test
 * @param input An input value to test against
 * @returns `true` if the test passes, otherwise it will throw.
 */
export declare function testSerializeThenParse<T>(parser: ParserBuilder<T>, input: T): boolean;

declare type UpdaterFn<T extends UseQueryStatesKeysMap> = (old: Values<T>) => Partial<Nullable<Values<T>>> | null;

declare type UpdateUrlFunction = (search: URLSearchParams, options: Required<AdapterOptions>) => void;
export { UpdateUrlFunction }
export { UpdateUrlFunction as unstable_UpdateUrlFunction }

export declare const URL_MAX_LENGTH = 2000;

/**
 * Helper type to define and reuse urlKey options to rename search params keys
 *
 * Usage:
 * ```ts
 * import { type UrlKeys } from 'nuqs' // or 'nuqs/server'
 *
 * export const coordinatesSearchParams = {
 *   latitude: parseAsFloat.withDefault(0),
 *   longitude: parseAsFloat.withDefault(0),
 * }
 * export const coordinatesUrlKeys: UrlKeys<typeof coordinatesSearchParams> = {
 *   latitude: 'lat',
 *   longitude: 'lng',
 * }
 *
 * // Later in the code:
 * useQueryStates(coordinatesSearchParams, {
 *   urlKeys: coordinatesUrlKeys
 * })
 * createSerializer(coordinatesSearchParams, {
 *   urlKeys: coordinatesUrlKeys
 * })
 * createSearchParamsCache(coordinatesSearchParams, {
 *   urlKeys: coordinatesUrlKeys
 * })
 * ```
 */
declare type UrlKeys<Parsers extends Record<string, any>> = Partial<Record<keyof Parsers, string>>;
export { UrlKeys }
export { UrlKeys as UrlKeys_alias_1 }
export { UrlKeys as UrlKeys_alias_2 }

export declare function urlSearchParamsToObject(search: URLSearchParams): Record<string, string | string[]>;

export declare type UrlUpdateEvent = {
    searchParams: URLSearchParams;
    queryString: string;
    options: Required<AdapterOptions>;
};

export declare function useAdapter(): AdapterInterface_2;

declare type UseAdapterHook = () => AdapterInterface;
export { UseAdapterHook }
export { UseAdapterHook as unstable_UseAdapterHook }

declare type UseNavigate = () => NavigateFn;

export declare function useNuqsNextAppRouterAdapter(): AdapterInterface;

export declare function useNuqsNextPagesRouterAdapter(): AdapterInterface;

declare const useOptimisticSearchParams: () => URLSearchParams;
export { useOptimisticSearchParams }
export { useOptimisticSearchParams as useOptimisticSearchParams_alias_2 }

export declare const useOptimisticSearchParams_alias_1: () => URLSearchParams;

export declare const useOptimisticSearchParams_alias_3: () => URLSearchParams;

/**
 * React state hook synchronized with a URL query string in Next.js
 *
 * This variant is used when providing a default value. This will make
 * the returned state non-nullable when the query is not present in the URL.
 * (the default value will be returned instead).
 *
 * _Note: the URL will **not** be updated with the default value if the query
 * is missing._
 *
 * Setting the value to `null` will clear the query in the URL, and return
 * the default value as state.
 *
 * Example usage:
 * ```ts
 *   const [count, setCount] = useQueryState(
 *     'count',
 *     parseAsInteger.defaultValue(0)
 *   )
 *
 *   const increment = () => setCount(oldCount => oldCount + 1)
 *   const decrement = () => setCount(oldCount => oldCount - 1)
 *   // Clears the query key from the URL and `count` equals 0
 *   const clearCountQuery = () => setCount(null)
 * ```
 * @param key The URL query string key to bind to
 * @param options - Parser (defines the state data type), default value and optional history mode.
 */
declare function useQueryState<T>(key: string, options: UseQueryStateOptions<T> & {
    defaultValue: T;
}): UseQueryStateReturn<NonNullable<ReturnType<typeof options.parse>>, typeof options.defaultValue>;

/**
 * React state hook synchronized with a URL query string in Next.js
 *
 * If the query is missing in the URL, the state will be `null`.
 *
 * Example usage:
 * ```ts
 *   // Blog posts filtering by tag
 *   const [tag, selectTag] = useQueryState('tag')
 *   const filteredPosts = posts.filter(post => tag ? post.tag === tag : true)
 *   const clearTag = () => selectTag(null)
 * ```
 * @param key The URL query string key to bind to
 * @param options - Parser (defines the state data type), and optional history mode.
 */
declare function useQueryState<T>(key: string, options: UseQueryStateOptions<T>): UseQueryStateReturn<NonNullable<ReturnType<typeof options.parse>>, undefined>;

/**
 * Default type string, limited options & default value
 */
declare function useQueryState(key: string, options: Options & {
    defaultValue: string;
}): UseQueryStateReturn<string, typeof options.defaultValue>;

/**
 * React state hook synchronized with a URL query string in Next.js
 *
 * If the query is missing in the URL, the state will be `null`.
 *
 * Note: by default the state type is a `string`. To use different types,
 * check out the `parseAsXYZ` helpers:
 * ```ts
 *   const [date, setDate] = useQueryState(
 *     'date',
 *     parseAsIsoDateTime.withDefault(new Date('2021-01-01'))
 *   )
 *
 *   const setToNow = () => setDate(new Date())
 *   const addOneHour = () => {
 *     setDate(oldDate => new Date(oldDate.valueOf() + 3600_000))
 *   }
 * ```
 * @param key The URL query string key to bind to
 * @param options - Parser (defines the state data type), and optional history mode.
 */
declare function useQueryState(key: string, options: Pick<UseQueryStateOptions<string>, keyof Options>): UseQueryStateReturn<string, undefined>;

/**
 * React state hook synchronized with a URL query string in Next.js
 *
 * If the query is missing in the URL, the state will be `null`.
 *
 * Note: by default the state type is a `string`. To use different types,
 * check out the `parseAsXYZ` helpers:
 * ```ts
 *   const [date, setDate] = useQueryState(
 *     'date',
 *     parseAsIsoDateTime.withDefault(new Date('2021-01-01'))
 *   )
 *
 *   const setToNow = () => setDate(new Date())
 *   const addOneHour = () => {
 *     setDate(oldDate => new Date(oldDate.valueOf() + 3600_000))
 *   }
 * ```
 * @param key The URL query string key to bind to
 */
declare function useQueryState(key: string): UseQueryStateReturn<string, undefined>;
export { useQueryState }
export { useQueryState as useQueryState_alias_1 }

declare interface UseQueryStateOptions<T> extends Parser<T>, Options {
}
export { UseQueryStateOptions }
export { UseQueryStateOptions as UseQueryStateOptions_alias_1 }

declare type UseQueryStateReturn<Parsed, Default> = [
Default extends undefined ? Parsed | null : Parsed,
(value: null | Parsed | ((old: Default extends Parsed ? Parsed : Parsed | null) => Parsed | null), options?: Options) => Promise<URLSearchParams>
];
export { UseQueryStateReturn }
export { UseQueryStateReturn as UseQueryStateReturn_alias_1 }

/**
 * Synchronise multiple query string arguments to React state in Next.js
 *
 * @param keys - An object describing the keys to synchronise and how to
 *               serialise and parse them.
 *               Use `parseAs(String|Integer|Float|...)` for quick shorthands.
 * @param options - Optional history mode, shallow routing and scroll restoration options.
 */
declare function useQueryStates<KeyMap extends UseQueryStatesKeysMap>(keyMap: KeyMap, { history, scroll, shallow, throttleMs, clearOnDefault, startTransition, urlKeys }?: Partial<UseQueryStatesOptions<KeyMap>>): UseQueryStatesReturn<KeyMap>;
export { useQueryStates }
export { useQueryStates as useQueryStates_alias_1 }

declare type UseQueryStatesKeysMap<Map = any> = {
    [Key in keyof Map]: KeyMapValue<Map[Key]>;
} & {};
export { UseQueryStatesKeysMap }
export { UseQueryStatesKeysMap as UseQueryStatesKeysMap_alias_1 }

declare type UseQueryStatesOptions<KeyMap extends UseQueryStatesKeysMap> = Options & {
    urlKeys: UrlKeys<KeyMap>;
};
export { UseQueryStatesOptions }
export { UseQueryStatesOptions as UseQueryStatesOptions_alias_1 }

declare type UseQueryStatesReturn<T extends UseQueryStatesKeysMap> = [
Values<T>,
SetValues<T>
];
export { UseQueryStatesReturn }
export { UseQueryStatesReturn as UseQueryStatesReturn_alias_1 }

declare type UseSearchParams = (initial: URLSearchParams) => [URLSearchParams, {}];

declare type Values<T extends UseQueryStatesKeysMap> = {
    [K in keyof T]: T[K]['defaultValue'] extends NonNullable<ReturnType<T[K]['parse']>> ? NonNullable<ReturnType<T[K]['parse']>> : ReturnType<T[K]['parse']> | null;
};
export { Values }
export { Values as Values_alias_1 }

export declare function warn(message: string, ...args: any[]): void;

export declare function warnIfURLIsTooLong(queryString: string): void;

/**
 * A higher order component that wraps the children with the NuqsTestingAdapter
 *
 * It allows creating wrappers for testing purposes by providing only the
 * necessary props to the NuqsTestingAdapter.
 *
 * Usage:
 * ```tsx
 * render(<MyComponent />, {
 *   wrapper: withNuqsTestingAdapter({ searchParams: '?foo=bar' })
 * })
 * ```
 */
export declare function withNuqsTestingAdapter(props?: Omit<TestingAdapterProps, 'children'>): ({ children }: {
    children: ReactNode;
}) => DetailedReactHTMLElement<InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>;

export { }
