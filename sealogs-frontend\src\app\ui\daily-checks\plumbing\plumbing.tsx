'use client'
import React, { useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import {
    <PERSON><PERSON>ield,
    CheckFieldContent,
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'

import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { useSearchParams } from 'next/navigation'
import {
    displayDescription,
    composeField,
    displayField,
    getFilteredFields,
    getSortOrder,
    getFieldLabel,
} from '@/app/ui/daily-checks/actions'

import 'react-quill/dist/quill.snow.css'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { useDebounceFn, useMediaQuery } from '@reactuses/core'
import CrewChecker from '../crew-checker/crew-checker'
import dayjs from 'dayjs'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetBody,
} from '@/components/ui/sheet'
import { Card } from '@/components/ui'
import { ReadSectionMemberComments } from './queries'

export default function Plumbing({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    setTab,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: any
    fieldImages: any
    refreshImages: any
}) {
    const router = useRouter()
    const searchParams = useSearchParams()
    // vesselID is kept for potential future use
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const tab = searchParams.get('tab') ?? ''
    const [saving, setSaving] = useState(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [sectionComment, setSectionComment] = useState<any>('')
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState<
        string | React.ReactNode
    >('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')
    const commentModel = new SectionMemberCommentModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    // isWide is kept for potential responsive design improvements
    const isWide = useMediaQuery('(min-width: 640px)')
    const [plumbingCrewResponsible, setPlumbingCrewResponsible] = useState<any>(
        vesselDailyCheck?.plumbingCrewResponsible?.nodes?.map(
            (member: any) => ({
                label: member.firstName + ' ' + member.surname,
                value: member.id,
            }),
        ),
    )
    const [plumbingCheckTime, setPlumbingCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.plumbingCheckTime ?? new Date()),
    )
    const handlePlumbingChecks = async (check: Boolean, value: any) => {
        if (+vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck.id,
                [value]: check ? 'Ok' : 'Not_Ok',
                logBookEntryID: +logentryID,
            }
            if (offline) {
                const data = await dailyCheckModel.save(variables)
                setVesselDailyCheck([data])
                setSaving(true)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing plumbing check', error)
            },
        },
    )

    // const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
    //     VesselDailyCheck_LogBookEntrySection,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data =
    //                 response.readVesselDailyCheck_LogBookEntrySections.nodes
    //             setVesselDailyCheck(data)
    //             setSaving(true)
    //         },
    //         onError: (error: any) => {
    //             console.error(
    //                 'VesselDailyCheck_LogBookEntrySection error',
    //                 error,
    //             )
    //         },
    //     },
    // )

    // handleSetTab is kept for API compatibility but not used in this component
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const handleSetTab = (tab: any) => {
        setTab(tab)
    }

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (_response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: (_response) => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        ReadSectionMemberComments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const fields = [
        {
            name: 'BilgeCheck',
            label: 'Bilge check',
            value: 'bilgeCheck',
            sortOrder: getSortOrder('BilgeCheck', logBookConfig),
            checked: vesselDailyCheck?.bilgeCheck,
        },
        {
            name: 'Sewage',
            label: 'Sewage',
            value: 'sewage',
            sortOrder: getSortOrder('Sewage', logBookConfig),
            checked: vesselDailyCheck?.sewage,
        },
        {
            name: 'FreshWater',
            label: 'Fresh water',
            value: 'freshWater',
            sortOrder: getSortOrder('FreshWater', logBookConfig),
            checked: vesselDailyCheck?.freshWater,
        },
        {
            name: 'Sanitation',
            label: 'Sanitation',
            value: 'sanitation',
            sortOrder: getSortOrder('Sanitation', logBookConfig),
            checked: vesselDailyCheck?.sanitation,
        },
        {
            name: 'PestControl',
            label: 'Pest control',
            value: 'pestControl',
            sortOrder: getSortOrder('PestControl', logBookConfig),
            checked: vesselDailyCheck?.pestControl,
        },
    ]

    // useEffect(() => {
    //     getSectionVesselDailyCheck_LogBookEntrySection({
    //         variables: {
    //             id: [vesselDailyCheck.id],
    //         },
    //     })
    // }, [tab])

    const handleSave = async () => {
        // Save the section data
        // if (offline) {
        //     const data = await dailyCheckModel.getByIds([vesselDailyCheck.id])
        //     setVesselDailyCheck(data)
        //     setSaving(true)
        // } else {
        //     getSectionVesselDailyCheck_LogBookEntrySection({
        //         variables: {
        //             id: [vesselDailyCheck.id],
        //         },
        //     })
        // }

        // Save the section comment
        const comment = sectionComment
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: 'Plumbing',
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }

        try {
            if (currentComment) {
                if (offline) {
                    await commentModel.save(variables)
                    loadSectionMemberComments()
                } else {
                    updateSectionMemberComment({
                        variables: { input: variables },
                    })
                }
            } else {
                if (comment) {
                    // Only create if there's a comment to save
                    if (offline) {
                        const offlineID = generateUniqueId()
                        await commentModel.save({ ...variables, id: offlineID })
                        loadSectionMemberComments()
                    } else {
                        createSectionMemberComment({
                            variables: { input: variables },
                        })
                    }
                }
            }
        } catch (error) {
            console.error('Error saving plumbing section', error)
        }
    }

    // These group change handlers are kept for potential future use with group fields
    // Currently not used as the group field UI has been commented out
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const handleGroupNoChange = (groupField: any, groupFieldParent: any) => {
        handlePlumbingChecks(
            false,
            fields.find((field: any) => field.name === groupFieldParent.name)
                ?.value,
        )
        groupField.map((field: any) => handlePlumbingChecks(false, field.value))
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const handleGroupYesChange = (groupField: any, groupFieldParent: any) => {
        handlePlumbingChecks(
            true,
            fields.find((field: any) => field.name === groupFieldParent.name)
                ?.value,
        )
        groupField.map((field: any) => handlePlumbingChecks(true, field.value))
    }

    const handlePlumbingCrewResponsible = async (crews: any) => {
        setPlumbingCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                plumbingCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handlePlumbingCheckTime = async (date: any) => {
        setPlumbingCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                plumbingCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('offline')
                /* const newVesselDailyCheck =
                                                await dailyCheckModel.save(variables)
                                            // setSaving(true)
                                            setSaving(false)
                                            setVesselDailyCheck([newVesselDailyCheck])
                                            const sections = logbook.logBookEntrySections.nodes
                                            const section = {
                                                className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                                                id: `${vesselDailyCheck.id}`,
                                                logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                                                __typename: 'VesselDailyCheck_LogBookEntrySection',
                                            }
                                            if (
                                                !sections.some(
                                                    (s: any) =>
                                                        JSON.stringify(s) === JSON.stringify(section),
                                                )
                                            ) {
                                                sections.push(section)
                                            }
                                            const lb = {
                                                ...logbook,
                                                logBookEntrySections: { nodes: sections },
                                            }
                                            await logBookModel.save(lb)
                                            getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        plumbingCheckTime:
                            dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    // useEffect(() => {
    //     getSectionVesselDailyCheck_LogBookEntrySection({
    //         variables: {
    //             id: [vesselDailyCheck.id],
    //         },
    //     })
    // }, [])

    const saveSectionComment = () => {
        getComment('Plumbing', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('Plumbing', 'Section')?.id,
                          comment: sectionComment,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'Plumbing',
                          comment: sectionComment,
                          logBookEntryID: +logentryID,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)

    const saveCheckTime = () => {
        if (vesselDailyCheck?.id > 0) {
            updateVesselDailyCheck_LogBookEntrySection({
                variables: {
                    input: {
                        id: vesselDailyCheck.id,
                        plumbingCheckTime:
                            dayjs(plumbingCheckTime).format('YYYY-MM-DD HH:mm'),
                    },
                },
            })
        }
    }
    const { run: debounceSaveCheckTime } = useDebounceFn(() => {
        saveCheckTime()
    }, 1000)

    return (
        <Card className="space-y-8">
            <CheckField>
                {(getFilteredFields(fields, false, logBookConfig).filter(
                    (field: any) => displayField(field.name, logBookConfig),
                ).length > 0 ||
                    getFilteredFields(fields, true, logBookConfig)?.filter(
                        (groupField: any) =>
                            displayField(groupField.name, logBookConfig),
                    )?.length > 0) && <CheckFieldTopContent />}
                <CheckFieldContent>
                    {logBookConfig && vesselDailyCheck && (
                        <>
                            {getFilteredFields(
                                fields,
                                false,
                                logBookConfig,
                            ).map((field: any, index: number) => (
                                <DailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    key={index}
                                    displayField={displayField(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={getFieldLabel(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    inputId={field.value}
                                    handleNoChange={() =>
                                        handlePlumbingChecks(false, field.value)
                                    }
                                    defaultNoChecked={
                                        field.checked === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handlePlumbingChecks(true, field.value)
                                    }
                                    defaultYesChecked={field.checked === 'Ok'}
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment(field.name),
                                            composeField(
                                                field.name,
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={getComment(field.name)?.comment}
                                    fieldId={field.value}
                                    displayImage={true}
                                    fieldImages={fieldImages}
                                    onImageUpload={refreshImages}
                                />
                            ))}
                            {getFilteredFields(fields, true, logBookConfig)
                                ?.filter((groupField: any) =>
                                    displayField(
                                        groupField.name,
                                        logBookConfig,
                                    ),
                                )
                                ?.map((groupField: any) => (
                                    <div key={groupField.name}>
                                        <>
                                            {/* {groupField.field?.title
                                                    ? groupField.field.title
                                                    : groupField.field.label}
                                                {displayDescription(
                                                    groupField.name,
                                                    logBookConfig,
                                                ) && (
                                                    <Button
                                                        variant="text"
                                                        size="icon"
                                                        iconLeft={AlertCircle}
                                                        iconOnly
                                                        className="ml-1"
                                                        onClick={() => {
                                                            setDescriptionPanelContent(
                                                                displayDescription(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                            setOpenDescriptionPanel(
                                                                true,
                                                            )
                                                            setDescriptionPanelHeading(
                                                                groupField.name,
                                                            )
                                                        }}
                                                    />
                                                )} */}
                                        </>
                                        <>
                                            {/* <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ).length > 0 &&
                                                            groupField?.items
                                                                ?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                )
                                                                ?.every(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        field.checked ===
                                                                        'Not_Ok',
                                                                )
                                                        }
                                                        defaultYesChecked={
                                                            groupField?.items?.filter(
                                                                (field: any) =>
                                                                    displayField(
                                                                        field.name,
                                                                        logBookConfig,
                                                                    ),
                                                            ).length > 0 &&
                                                            groupField?.items
                                                                ?.filter(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                )
                                                                ?.every(
                                                                    (
                                                                        field: any,
                                                                    ) =>
                                                                        field.checked ===
                                                                        'Ok',
                                                                )
                                                        }
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    /> */}
                                        </>
                                        {groupField?.items?.map(
                                            (field: any, index: number) => (
                                                <DailyCheckField
                                                    locked={
                                                        locked ||
                                                        !edit_logBookEntry
                                                    }
                                                    key={index}
                                                    displayField={displayField(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    displayDescription={displayDescription(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    setDescriptionPanelContent={
                                                        setDescriptionPanelContent
                                                    }
                                                    setOpenDescriptionPanel={
                                                        setOpenDescriptionPanel
                                                    }
                                                    setDescriptionPanelHeading={
                                                        setDescriptionPanelHeading
                                                    }
                                                    displayLabel={getFieldLabel(
                                                        field.name,
                                                        logBookConfig,
                                                    )}
                                                    inputId={field.value}
                                                    handleNoChange={() =>
                                                        handlePlumbingChecks(
                                                            false,
                                                            field.value,
                                                        )
                                                    }
                                                    defaultNoChecked={
                                                        field.checked ===
                                                        'Not_Ok'
                                                    }
                                                    handleYesChange={() =>
                                                        handlePlumbingChecks(
                                                            true,
                                                            field.value,
                                                        )
                                                    }
                                                    defaultYesChecked={
                                                        field.checked === 'Ok'
                                                    }
                                                    commentAction={() =>
                                                        showCommentPopup(
                                                            getComment(
                                                                field.name,
                                                            ),
                                                            composeField(
                                                                field.name,
                                                                logBookConfig,
                                                            ),
                                                        )
                                                    }
                                                    comment={
                                                        getComment(field.name)
                                                            ?.comment
                                                    }
                                                    fieldId={field.value}
                                                    displayImage={true}
                                                    fieldImages={fieldImages}
                                                    onImageUpload={
                                                        refreshImages
                                                    }
                                                />
                                            ),
                                        )}
                                    </div>
                                ))}
                        </>
                    )}
                </CheckFieldContent>
            </CheckField>
            <CrewChecker
                vesselDailyCheckID={vesselDailyCheck.id}
                crewKey="PlumbingCrewResponsible"
                timeKey="PlumbingCheckTime"
                logBookConfig={logBookConfig}
                locked={locked}
                offline={offline}
                edit_logBookEntry={edit_logBookEntry}
                setCrewResponsible={handlePlumbingCrewResponsible}
                crewResponsible={plumbingCrewResponsible}
                checkTime={plumbingCheckTime}
                handleCheckTime={handlePlumbingCheckTime}
                // setCheckTime={setPlumbingCheckTime}
                setCheckTime={(e) => {
                    setPlumbingCheckTime(e)
                    debounceSaveCheckTime()
                }}
            />
            <Label label="Comments">
                <Textarea
                    id={`section_comment`}
                    rows={4}
                    disabled={locked || !edit_logBookEntry}
                    className={''}
                    placeholder="Comments ..."
                    onChange={(e) => {
                        setSectionComment(e.target.value)
                        debounceSaveSectionComment()
                    }}
                    defaultValue={getComment('Plumbing', 'Section')?.comment}
                />
            </Label>
            {/* <ActionFooter
                showFooter={!locked || edit_logBookEntry}
                onCreateTask={handleCreateTask}
                onSave={handleSave}
                createTaskLoading={createMaintenanceCheckLoading}
                createTaskDisabled={createMaintenanceCheckLoading}
                showSave={true}
            /> */}
            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked || !edit_logBookEntry}
                actionText="Save"
                title="Comment"
                size="md">
                <div className="flex flex-col">
                    <Textarea
                        id="comment"
                        readOnly={locked || !edit_logBookEntry}
                        disabled={locked || !edit_logBookEntry}
                        rows={4}
                        className="w-full"
                        placeholder="Comment"
                        onChange={(e) => setSectionComment(e.target.value)}
                        defaultValue={
                            currentComment ? currentComment.comment : ''
                        }
                    />
                </div>
            </AlertDialogNew>
            <Sheet
                open={openDescriptionPanel}
                onOpenChange={(open) => {
                    setOpenDescriptionPanel(open)
                    if (!open) {
                        setDescriptionPanelContent('')
                        setDescriptionPanelHeading('')
                    }
                }}>
                <SheetContent side="left">
                    <SheetHeader>
                        <SheetTitle>
                            Field -{' '}
                            <span className="font-normal">
                                {descriptionPanelHeading}
                            </span>
                        </SheetTitle>
                    </SheetHeader>
                    <SheetBody>
                        {typeof descriptionPanelContent === 'string' ? (
                            <div
                                className="prose prose-sm max-w-none"
                                dangerouslySetInnerHTML={{
                                    __html: descriptionPanelContent as string,
                                }}
                            />
                        ) : (
                            <div className="prose prose-sm max-w-none">
                                {descriptionPanelContent}
                            </div>
                        )}
                    </SheetBody>
                </SheetContent>
            </Sheet>
        </Card>
    )
}
