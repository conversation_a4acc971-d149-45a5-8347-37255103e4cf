type ValidQueueKeys = {
    [K in keyof Window]-?: NonNullable<Window[K]> extends {
        push(...args: any[]): any;
    } ? K : never;
}[keyof Window];
/**
 * Registers a queue that can be filled with data before it has actually been initialized with this function.
 * Before calling this function, `window[key]` can just be handled as an array of data.
 * When calling this funcation, all accumulated data will be passed to the callback.
 * After calling this function, `window[key]` will be an object with a `push` method that will call the callback with the data.
 */
export declare function registerLateInitializingQueue<K extends ValidQueueKeys>(key: K, callback: (data: Parameters<NonNullable<Window[K]>["push"]>[0]) => void): void;
export {};
//# sourceMappingURL=lateInitializingQueue.d.ts.map