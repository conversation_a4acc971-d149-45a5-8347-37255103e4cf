import { test, expect } from '@playwright/test';

test.describe('SeaLogs Application', () => {
  test('homepage loads correctly', async ({ page }) => {
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if the page title contains SeaLogs
    await expect(page).toHaveTitle(/SeaLogs/i);
    
    // Take a screenshot for visual verification
    await page.screenshot({ path: 'screenshots/homepage.png', fullPage: true });
  });

  test('login page elements are visible', async ({ page }) => {
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check for common login elements (adjust selectors based on your actual app)
    const emailInput = page.locator('input[type="email"], input[name="email"], input[placeholder*="email" i]');
    const passwordInput = page.locator('input[type="password"], input[name="password"], input[placeholder*="password" i]');
    const loginButton = page.locator('button:has-text("Login"), button:has-text("Sign in"), input[type="submit"]');
    
    // Wait for at least one login element to be visible
    await expect(emailInput.or(passwordInput).or(loginButton).first()).toBeVisible({ timeout: 10000 });
    
    // Take a screenshot
    await page.screenshot({ path: 'screenshots/login-page.png' });
  });

  test('navigation is responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    await page.waitForLoadState('networkidle');
    
    // Look for mobile menu indicators (hamburger menu, mobile nav, etc.)
    const mobileMenu = page.locator('[data-testid="mobile-menu"], .mobile-menu, button[aria-label*="menu" i], .hamburger');
    const navToggle = page.locator('button:has-text("Menu"), .nav-toggle, .menu-toggle');
    
    // Check if mobile navigation elements exist
    const hasMobileNav = await mobileMenu.or(navToggle).first().isVisible().catch(() => false);
    
    if (hasMobileNav) {
      console.log('Mobile navigation detected');
    }
    
    // Take mobile screenshot
    await page.screenshot({ path: 'screenshots/mobile-view.png' });
  });

  test('page loads without console errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    // Listen for console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait a bit more for any async operations
    await page.waitForTimeout(2000);
    
    // Check that there are no critical console errors
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED')
    );
    
    if (criticalErrors.length > 0) {
      console.log('Console errors found:', criticalErrors);
    }
    
    // This is informational - adjust based on your app's expected behavior
    expect(criticalErrors.length).toBeLessThan(5);
  });

  test('basic accessibility check', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for basic accessibility elements
    const hasHeadings = await page.locator('h1, h2, h3').count() > 0;
    const hasLabels = await page.locator('label').count() > 0;
    const hasButtons = await page.locator('button, input[type="button"], input[type="submit"]').count() > 0;
    
    expect(hasHeadings || hasLabels || hasButtons).toBeTruthy();
    
    // Take screenshot
    await page.screenshot({ path: 'screenshots/accessibility-check.png' });
  });
});

test.describe('Safari-specific tests', () => {
  test('webkit rendering check', async ({ page, browserName }) => {
    // Only run this test on webkit
    test.skip(browserName !== 'webkit', 'This test is only for Safari/WebKit');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for Safari-specific rendering
    const bodyStyles = await page.locator('body').evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        fontFamily: styles.fontFamily,
        fontSize: styles.fontSize,
      };
    });
    
    expect(bodyStyles.fontFamily).toBeTruthy();
    expect(bodyStyles.fontSize).toBeTruthy();
    
    // Take Safari-specific screenshot
    await page.screenshot({ path: 'screenshots/safari-rendering.png' });
  });
});
