'use client'
import React, { useEffect, useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import {
    DailyCheckField,
    CheckField,
    CheckFieldTopContent,
    CheckFieldContent,
} from '@/components/daily-check-field'

import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
// useRouter removed as it's no longer needed with ActionFooter
import { useSearchParams } from 'next/navigation'
import {
    displayDescription,
    composeField,
    displayField,
    getFilteredFields,
    getSortOrder,
    getFieldLabel,
} from '@/app/ui/daily-checks/actions'
import 'react-quill/dist/quill.snow.css'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDaily<PERSON>heck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { identity, set } from 'lodash'
import { useDebounceFn, useMediaQuery } from '@reactuses/core'
import CrewChecker from '../crew-checker/crew-checker'
import dayjs from 'dayjs'
import { Label } from '@/components/ui/label'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { AlertCircle } from 'lucide-react'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ReadSectionMemberComments } from './queries'

export default function Biosecurity({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    // setTab is unused but kept for API compatibility
    setTab,
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab: any
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages: any
    refreshImages: any
}) {
    // Router removed as it's no longer needed with ActionFooter
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const tab = searchParams.get('tab') ?? ''
    const [saving, setSaving] = useState(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [sectionComment, setSectionComment] = useState('')
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    // Using setBiosecGlueBoardTraps but not reading biosecGlueBoardTraps directly
    const [, setBiosecGlueBoardTraps] = useState<Boolean>(
        vesselDailyCheck?.biosecGlueBoardTraps === 'Ok',
    )
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')

    // Create wrapper functions to handle type conversion for DailyCheckField props
    const handleSetDescriptionPanelContent = (
        content: string | React.ReactNode,
    ) => {
        setDescriptionPanelContent(content as string)
    }

    const handleSetOpenDescriptionPanel = (open: boolean) => {
        setOpenDescriptionPanel(open)
    }

    const handleSetDescriptionPanelHeading = (heading: string) => {
        setDescriptionPanelHeading(heading)
    }

    // useEffect(() => {
    //     getSectionVesselDailyCheck_LogBookEntrySection({
    //         variables: {
    //             id: [vesselDailyCheck.id],
    //         },
    //     })
    // }, [tab])

    const commentModel = new SectionMemberCommentModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    const isWide = useMediaQuery('(min-width: 640px)')
    const [biosecCrewResponsible, setBiosecurityCrewResponsible] =
        useState<any>(
            vesselDailyCheck?.biosecCrewResponsible?.nodes?.map(
                (member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                }),
            ),
        )
    const [biosecCheckTime, setBiosecurityCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.biosecCheckTime ?? new Date()),
    )
    const handleBiosecurityChecks = async (check: Boolean, value: any) => {
        if (+vesselDailyCheck?.id > 0) {
            const variable = {
                id: vesselDailyCheck.id,
                [value]: check ? 'Ok' : 'Not_Ok',
            }
            if (offline) {
                const data = await dailyCheckModel.save(variable)
                setVesselDailyCheck([data])
                // setSaving(true)
                setSaving(false)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variable,
                    },
                })
            }
        }
    }

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('Error completing safety check', error)
            },
        },
    )

    // const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
    //     VesselDailyCheck_LogBookEntrySection,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data =
    //                 response.readVesselDailyCheck_LogBookEntrySections.nodes
    //             setVesselDailyCheck(data)
    //             setSaving(true)
    //         },
    //         onError: (error: any) => {
    //             console.error(
    //                 'VesselDailyCheck_LogBookEntrySection error',
    //                 error,
    //             )
    //         },
    //     },
    // )

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        ReadSectionMemberComments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const fields = [
        {
            name: 'BiosecGlueBoardTraps',
            label: 'BiosecGlueBoardTraps',
            value: 'biosecGlueBoardTraps',
            checked: vesselDailyCheck?.biosecGlueBoardTraps,
            sortOrder: getSortOrder('BiosecGlueBoardTraps', logBookConfig),
        },
    ]

    const handleSave = async () => {
        // if (+vesselDailyCheck.id > 0) {
        //     if (offline) {
        //         const data = await dailyCheckModel.getByIds([
        //             vesselDailyCheck.id,
        //         ])
        //         setVesselDailyCheck(data)
        //         setSaving(true)
        //     } else {
        //         getSectionVesselDailyCheck_LogBookEntrySection({
        //             variables: {
        //                 id: [vesselDailyCheck.id],
        //             },
        //         })
        //     }
        // }

        const comment = sectionComment
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: 'Biosecurity',
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const handleBiosecurityCrewResponsible = async (crews: any) => {
        setBiosecurityCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                biosecCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log('offline')
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        biosecCrewResponsible: {
                            nodes: crewResponsibleIDs,
                        },
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleBiosecurityCheckTime = async (date: any) => {
        setBiosecurityCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                biosecCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log('offline')
                /* const newVesselDailyCheck =
                                            await dailyCheckModel.save(variables)
                                        // setSaving(true)
                                        setSaving(false)
                                        setVesselDailyCheck([newVesselDailyCheck])
                                        const sections = logbook.logBookEntrySections.nodes
                                        const section = {
                                            className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                                            id: `${vesselDailyCheck.id}`,
                                            logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                                            __typename: 'VesselDailyCheck_LogBookEntrySection',
                                        }
                                        if (
                                            !sections.some(
                                                (s: any) =>
                                                    JSON.stringify(s) === JSON.stringify(section),
                                            )
                                        ) {
                                            sections.push(section)
                                        }
                                        const lb = {
                                            ...logbook,
                                            logBookEntrySections: { nodes: sections },
                                        }
                                        await logBookModel.save(lb)
                                        getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        biosecCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    // useEffect(() => {
    //     getSectionVesselDailyCheck_LogBookEntrySection({
    //         variables: {
    //             id: [vesselDailyCheck.id],
    //         },
    //     })
    // }, [])

    const saveSectionComment = () => {
        getComment('Biosecurity', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('Biosecurity', 'Section')?.id,
                          comment: sectionComment,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'Biosecurity',
                          comment: sectionComment,
                          logBookEntryID: +logentryID,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)

    const saveCheckTime = () => {
        if (vesselDailyCheck?.id > 0) {
            updateVesselDailyCheck_LogBookEntrySection({
                variables: {
                    input: {
                        id: vesselDailyCheck.id,
                        biosecCheckTime:
                            dayjs(biosecCheckTime).format('YYYY-MM-DD HH:mm'),
                    },
                },
            })
        }
    }
    const { run: debounceSaveCheckTime } = useDebounceFn(() => {
        saveCheckTime()
    }, 1000)

    return (
        <>
            <Card className="space-y-6">
                {logBookConfig && vesselDailyCheck && (
                    <>
                        <CheckField>
                            {getFilteredFields(
                                fields,
                                false,
                                logBookConfig,
                            ).filter((field: any) =>
                                displayField(field.name, logBookConfig),
                            ).length > 0 && <CheckFieldTopContent />}
                            <CheckFieldContent>
                                {getFilteredFields(
                                    fields,
                                    false,
                                    logBookConfig,
                                ).map((field: any, index: number) => (
                                    <DailyCheckField
                                        locked={locked || !edit_logBookEntry}
                                        key={index}
                                        displayField={displayField(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        displayDescription={displayDescription(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        setDescriptionPanelContent={
                                            handleSetDescriptionPanelContent
                                        }
                                        setOpenDescriptionPanel={
                                            handleSetOpenDescriptionPanel
                                        }
                                        setDescriptionPanelHeading={
                                            handleSetDescriptionPanelHeading
                                        }
                                        displayLabel={getFieldLabel(
                                            field.name,
                                            logBookConfig,
                                        )}
                                        inputId={field.value}
                                        handleNoChange={() =>
                                            handleBiosecurityChecks(
                                                false,
                                                field.value,
                                            )
                                        }
                                        defaultNoChecked={
                                            field.checked === 'Not_Ok'
                                        }
                                        handleYesChange={() =>
                                            handleBiosecurityChecks(
                                                true,
                                                field.value,
                                            )
                                        }
                                        defaultYesChecked={
                                            field.checked === 'Ok'
                                        }
                                        commentAction={() =>
                                            showCommentPopup(
                                                getComment(field.name),
                                                composeField(
                                                    field.name,
                                                    logBookConfig,
                                                ),
                                            )
                                        }
                                        comment={
                                            getComment(field.name)?.comment
                                        }
                                        displayImage={true}
                                        fieldImages={fieldImages}
                                        onImageUpload={refreshImages}
                                    />
                                ))}
                            </CheckFieldContent>
                        </CheckField>

                        {getFilteredFields(fields, true, logBookConfig)
                            ?.filter((groupField: any) =>
                                displayField(groupField.name, logBookConfig),
                            )
                            ?.map((groupField: any) => (
                                <CheckField
                                    key={groupField.name}
                                    className="mt-4">
                                    <div className="flex items-center mb-3">
                                        <h3 className="text-lg">
                                            {groupField.field?.title
                                                ? groupField.field.title
                                                : groupField.field.label}
                                        </h3>
                                        {displayDescription(
                                            groupField.name,
                                            logBookConfig,
                                        ) && (
                                            <Button
                                                variant="text"
                                                size="icon"
                                                iconLeft={AlertCircle}
                                                iconOnly
                                                onClick={() => {
                                                    handleSetDescriptionPanelContent(
                                                        displayDescription(
                                                            groupField.name,
                                                            logBookConfig,
                                                        ),
                                                    )
                                                    handleSetOpenDescriptionPanel(
                                                        true,
                                                    )
                                                    handleSetDescriptionPanelHeading(
                                                        groupField.name,
                                                    )
                                                }}
                                            />
                                        )}
                                    </div>
                                    {groupField?.items?.map(
                                        (field: any, index: number) => (
                                            <DailyCheckField
                                                locked={
                                                    locked || !edit_logBookEntry
                                                }
                                                key={index}
                                                displayField={displayField(
                                                    field.name,
                                                    logBookConfig,
                                                )}
                                                displayDescription={displayDescription(
                                                    field.name,
                                                    logBookConfig,
                                                )}
                                                setDescriptionPanelContent={
                                                    handleSetDescriptionPanelContent
                                                }
                                                setOpenDescriptionPanel={
                                                    handleSetOpenDescriptionPanel
                                                }
                                                setDescriptionPanelHeading={
                                                    handleSetDescriptionPanelHeading
                                                }
                                                displayLabel={getFieldLabel(
                                                    field.name,
                                                    logBookConfig,
                                                )}
                                                inputId={field.value}
                                                handleNoChange={() =>
                                                    field.handleChange(false)
                                                }
                                                defaultNoChecked={
                                                    field.checked === 'Not_Ok'
                                                }
                                                handleYesChange={() =>
                                                    field.handleChange(true)
                                                }
                                                defaultYesChecked={
                                                    field.checked === 'Ok'
                                                }
                                                commentAction={() =>
                                                    showCommentPopup(
                                                        getComment(field.name),
                                                        composeField(
                                                            field.name,
                                                            logBookConfig,
                                                        ),
                                                    )
                                                }
                                                comment={
                                                    getComment(field.name)
                                                        ?.comment
                                                }
                                                displayImage={true}
                                                fieldImages={fieldImages}
                                                onImageUpload={refreshImages}
                                            />
                                        ),
                                    )}
                                </CheckField>
                            ))}
                    </>
                )}

                <CrewChecker
                    vesselDailyCheckID={vesselDailyCheck.id}
                    crewKey="BiosecCrewResponsible"
                    timeKey="BiosecCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handleBiosecurityCrewResponsible}
                    crewResponsible={biosecCrewResponsible}
                    checkTime={biosecCheckTime}
                    handleCheckTime={handleBiosecurityCheckTime}
                    // setCheckTime={setBiosecurityCheckTime}
                    setCheckTime={(e) => {
                        setBiosecurityCheckTime(e)
                        debounceSaveCheckTime()
                    }}
                />

                <Label htmlFor="section_comment" label="Comments">
                    <Textarea
                        id="section_comment"
                        disabled={locked || !edit_logBookEntry}
                        rows={4}
                        placeholder="Comments ..."
                        onChange={(e) => {
                            setSectionComment(e.target.value)
                            debounceSaveSectionComment()
                        }}
                        // onBlur={(e) =>

                        // }
                        defaultValue={
                            getComment('Biosecurity', 'Section')?.comment
                        }
                    />
                </Label>
            </Card>

            {/* <ActionFooter
                showFooter={!locked || edit_logBookEntry}
                onCreateTask={handleCreateTask}
                onSave={handleSave}
                createTaskLoading={createMaintenanceCheckLoading}
                createTaskDisabled={createMaintenanceCheckLoading}
            /> */}
            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                actionText="Save">
                <div className="space-y-4">
                    <Label htmlFor="comment">Comment</Label>
                    <Textarea
                        readOnly={locked || !edit_logBookEntry}
                        id="comment"
                        rows={4}
                        placeholder="Comment"
                        defaultValue={
                            currentComment ? currentComment.comment : ''
                        }
                        disabled={locked || !edit_logBookEntry}
                    />
                </div>
            </AlertDialogNew>
            <Sheet
                open={openDescriptionPanel}
                onOpenChange={(open) => {
                    setOpenDescriptionPanel(open)
                    if (!open) {
                        setDescriptionPanelContent('')
                        setDescriptionPanelHeading('')
                    }
                }}>
                <SheetContent
                    side="left"
                    className={isWide ? 'w-[60vw]' : 'w-[90vw]'}>
                    <SheetHeader className="space-y-4">
                        <SheetTitle>
                            Field -{' '}
                            <span className="font-light">
                                {descriptionPanelHeading}
                            </span>
                        </SheetTitle>
                    </SheetHeader>

                    <div className="mt-6 overflow-y-auto">
                        <div
                            className="prose prose-sm max-w-none"
                            dangerouslySetInnerHTML={{
                                __html: descriptionPanelContent,
                            }}
                        />
                    </div>
                </SheetContent>
            </Sheet>
        </>
    )
}
