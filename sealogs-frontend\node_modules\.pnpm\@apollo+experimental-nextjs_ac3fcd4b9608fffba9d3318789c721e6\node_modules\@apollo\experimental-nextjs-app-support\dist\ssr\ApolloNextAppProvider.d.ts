import * as React from "react";
import type { ApolloClient } from "@apollo/client";
import type { HydrationContextOptions } from "./RehydrationContext";
export declare const ApolloClientSingleton: unique symbol;
declare global {
    interface Window {
        [ApolloClientSingleton]?: ApolloClient<any>;
    }
}
export declare const ApolloNextAppProvider: ({ makeClient, children, ...hydrationContextOptions }: React.PropsWithChildren<{
    makeClient: () => ApolloClient<any>;
} & HydrationContextOptions>) => React.JSX.Element;
//# sourceMappingURL=ApolloNextAppProvider.d.ts.map