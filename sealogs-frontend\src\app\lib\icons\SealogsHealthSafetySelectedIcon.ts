const React = require('react')

export function SealogsHealthSafetySelectedIcon({ ...props }) {
//    return /*#__PURE__*/ React.createElement(
//        'svg',
//        Object.assign(
//            {
//                className: '-ml-1.5 mr-2 h-6 w-6',
//                xmlns: 'http://www.w3.org/2000/svg',
//                viewBox: '-4 0 26 20',
//            },
//            props,
//        ),
//        /*#__PURE__*/ React.createElement('rect', {
//            x: '.2658',
//            y: '5.9435',
//            width: '18.6775',
//            height: '12.0407',
//            rx: '.6387',
//            ry: '.6387',
//            fill: '#f2f4f7',
//            stroke: '#022450',
//            strokeMiterlimit: '10',
//            strokeWidth: '0.25px',
//        }),
//        /*#__PURE__*/ React.createElement('path', {
//            d: 'M19.084,17.3455V3.2669c0-.4297-.3497-.7795-.7795-.7795h-9.5224L7.0224.1531c-.0132-.0177-.0341-.028-.0561-.028H.9045C.4747.125.125.4748.125.9045v16.441c0,.4297.3497.7795.7795.7795h17.4001c.4297,0,.7795-.3497.7795-.7795ZM3.2614,5.8028H.9045c-.2644,0-.4977.133-.6387.3349V.9045C.2658.5523.5523.2658.9045.2658h6.0267l1.7597,2.3343c.0132.0177.0341.028.0561.028h9.5576c.3522,0,.6387.2865.6387.6387v2.8707c-.141-.2019-.3743-.3349-.6387-.3349h-2.3569M15.8069,5.8028H3.4022M.2658,17.3455V6.5821c0-.3521.2865-.6386.6387-.6386h17.4001c.3522,0,.6387.2865.6387.6386v10.7634c0,.3522-.2865.6387-.6387.6387H.9045c-.3522,0-.6387-.2865-.6387-.6387Z',
//            fill: '#022450',
//            stroke: '#022450',
//            strokeMiterlimit: '10',
//            strokeWidth: '0.25px',
//        }),
//        /*#__PURE__*/ React.createElement('path', {
//            d: 'M13.3511,13.1097h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.6351h-1.9696v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.6351v-1.9694h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.6351h1.9696v2.6351c0,.0701.0569.1268.1268.1268h2.6351v1.9694Z',
//            fill: '#ffffff',
//            stroke: '#022450',
//            strokeMiterlimit: '10',
//            strokeWidth: '0.3885px',
//        }),
//        /*#__PURE__*/ React.createElement('path', {
//            d: 'M13.4779,10.8868h-2.6351v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.2231c-.0698,0-.1268.0567-.1268.1268v2.6351h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.2229c0,.0701.0569.1268.1268.1268h2.6351v2.6351c0,.0701.0569.1268.1268.1268h2.2231c.0698,0,.1268-.0567.1268-.1268v-2.6351h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.2229c0-.0701-.0569-.1268-.1268-.1268ZM13.3511,13.1097h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.6351h-1.9696v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.6351v-1.9694h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.6351h1.9696v2.6351c0,.0701.0569.1268.1268.1268h2.6351v1.9694Z',
//            fill: '#022450',
//            stroke: '#022450',
//            strokeMiterlimit: '10',
//            strokeWidth: '0.3885px',
//        }),
//    )

    return React.createElement('img', {
        src: '/sealogs-V2_Icons/health-selected-light.svg',
        alt: 'Documents Icon',
        ...props,
    });
}
