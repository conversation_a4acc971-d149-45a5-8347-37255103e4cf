'use client'
import { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import {
    CREATE_FUELLOG,
    CREATE_R2FILE,
    CreateRefuellingBunkering,
    CreateTripEvent,
    CreateTripReport_LogBookEntrySection,
    UPDATE_FUELLOG,
    UPDATE_FUELTANK,
} from '@/app/lib/graphQL/mutation'
import dynamic from 'next/dynamic'

import FuelTankModel from '@/app/offline/models/fuelTank'
import FuelGauge from '../fuel-gauge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useSearchParams } from 'next/navigation'
import { AlertDialogNew, Textarea } from '@/components/ui'
import LocationField from '../../logbook/components/location'
import TimeField from '../../logbook/components/time'
import dayjs from 'dayjs'
import UploadCloudFlare from '../../logbook/components/upload-cf'
import { toast } from 'sonner'
import {
    ReadOneLogBookEntry,
    ReadTripReport_LogBookEntrySections,
} from './queries'

const GaugeComponent = dynamic(() => import('react-gauge-component'), {
    ssr: false,
})

export default function VesselFuelStatus({
    fuelTankList,
    updateFuelLogList,
    updateTripReport,
    updateFuelTankList,
    offline = false,
    fuelLogs,
}: {
    fuelTankList: any
    updateFuelLogList: any
    updateTripReport?: any
    updateFuelTankList: any
    offline?: boolean
    fuelLogs: any
}) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const [openFuelTankDialog, setOpenFuelTankDialog] = useState(false)
    const [currentFuelTank, setCurrentFuelTank] = useState<any>(null)
    const [tripReport, setTripReport] = useState<any>([])
    const [openRefuelling, setOpenRefuelling] = useState(false)
    const [refuelling, setRefuelling] = useState<any>({
        fuelAdded: 0,
        costPerLitre: 0,
        totalCost: 0,
    })
    const [currentLocation, setCurrentLocation] = useState<any>(null)
    const [fuelReceipts, setFuelReceipts] = useState<any>([])
    const fuelTankModel = new FuelTankModel()

    const [getSectionTripReport_LogBookEntrySection] = useLazyQuery(
        ReadTripReport_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                setTripReport(data)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const handleSetLogbook = async (logbook: any) => {
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))
        sectionTypes.forEach(async (section: any) => {
            if (
                section.className === 'SeaLogs\\TripReport_LogBookEntrySection'
            ) {
                getSectionTripReport_LogBookEntrySection({
                    variables: {
                        id: section.ids,
                    },
                })
            }
        })
    }

    const [queryLogBookEntry] = useLazyQuery(ReadOneLogBookEntry, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                handleSetLogbook(data)
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookEntry error', error)
        },
    })

    useEffect(() => {
        queryLogBookEntry({
            variables: {
                logbookEntryId: +logentryID,
            },
        })
    }, [])

    const handleUpdateFuelTank = async () => {
        if (offline) {
            await fuelTankModel.save({
                id: currentFuelTank.id,
                currentLevel: +currentFuelTank.currentLevel,
            })
            // const fuelTankIds = fuelTankList.map((tank: any) => tank.id)
            // updateFuelLogList(fuelTankIds)
            setOpenFuelTankDialog(false)
        } else {
            const fuelLog = fuelLogs
                .filter((log: any) => log.fuelTank.id == currentFuelTank.id)
                .sort(
                    (a: any, b: any) =>
                        new Date(a.date).getTime() - new Date(b.date).getTime(),
                )[0]
            const Tasking = tripReport.flatMap((trip: any) =>
                trip.tripEvents.nodes
                    .filter((event: any) => event.eventCategory === 'Tasking')
                    .flatMap((event: any) =>
                        event.eventType_Tasking.fuelLog.nodes
                            .map((log: any) => log)
                            .filter(
                                (log: any) =>
                                    log.fuelTank.id == currentFuelTank.id,
                            ),
                    ),
            )
            const RefuellingBunkering = tripReport.flatMap((trip: any) =>
                trip.tripEvents.nodes
                    .filter(
                        (event: any) =>
                            event.eventCategory === 'RefuellingBunkering',
                    )
                    .flatMap((event: any) =>
                        event.eventType_RefuellingBunkering.fuelLog.nodes
                            .map((log: any) => log)
                            .filter(
                                (log: any) =>
                                    log.fuelTank.id == currentFuelTank.id,
                            ),
                    ),
            )
            const PassengerDropFacility = tripReport.flatMap((trip: any) =>
                trip.tripEvents.nodes
                    .filter(
                        (event: any) =>
                            event.eventCategory === 'PassengerDropFacility',
                    )
                    .flatMap((event: any) =>
                        event.eventType_PassengerDropFacility.fuelLog.nodes
                            .map((log: any) => log)
                            .filter(
                                (log: any) =>
                                    log.fuelTank.id == currentFuelTank.id,
                            ),
                    ),
            )

            if (
                [...Tasking, ...RefuellingBunkering, ...PassengerDropFacility]
                    .length > 0
            ) {
                const fuelRange = [
                    ...Tasking,
                    ...RefuellingBunkering,
                    ...PassengerDropFacility,
                ].reduce(
                    (range, log: any) => {
                        const fuelBefore = +log.fuelBefore
                        const fuelAfter = +log.fuelAfter
                        return {
                            min: Math.min(range.min, fuelBefore, fuelAfter),
                            max: Math.max(range.max, fuelBefore, fuelAfter),
                        }
                    },
                    { min: Infinity, max: -Infinity },
                )

                const minFuel = fuelRange.min
                const maxFuel = fuelRange.max

                const fuelChanged =
                    +currentFuelTank.currentLevel - +fuelLog.fuelBefore

                if (maxFuel + fuelChanged > +currentFuelTank.capacity) {
                    toast.error(
                        'Fuel tank capacity exceeded. Please check the fuel level.',
                    )
                    return
                }
                if (minFuel + fuelChanged < 0) {
                    toast.error(
                        'Fuel tank level cannot be negative. Please check the fuel level.',
                    )
                    return
                }

                const allFuelLogs = [
                    ...Tasking,
                    ...RefuellingBunkering,
                    ...PassengerDropFacility,
                ]

                allFuelLogs.forEach((log: any) => {
                    if (log.fuelTank.id == currentFuelTank.id) {
                        updateFuelLog({
                            variables: {
                                input: {
                                    id: log.id,
                                    fuelAfter: +log.fuelAfter + fuelChanged,
                                    fuelBefore: +log.fuelBefore + fuelChanged,
                                },
                            },
                        })
                    }
                })
                const currentLevel = +allFuelLogs[0].fuelTank.currentLevel
                updateFuelTank({
                    variables: {
                        input: {
                            id: currentFuelTank.id,
                            currentLevel: currentLevel + fuelChanged,
                        },
                    },
                })
            }
            updateFuelLog({
                variables: {
                    input: {
                        id: fuelLog.id,
                        fuelAfter: +currentFuelTank.currentLevel,
                        fuelBefore: +currentFuelTank.currentLevel,
                    },
                },
            })
            setOpenFuelTankDialog(false)
        }
    }

    const [updateFuelLog] = useMutation(UPDATE_FUELLOG, {
        onCompleted: (data) => {
            const fuelLogIds = fuelLogs.map((log: any) => log.id)
            updateFuelLogList(fuelLogIds)
        },
        onError: (error) => {
            console.error(error)
        },
    })

    const [updateFuelTank] = useMutation(UPDATE_FUELTANK, {
        onCompleted: (data) => {
            // const fuelTankIds = fuelTankList.map((tank: any) => tank.id)
            // updateFuelTankList(fuelTankIds)
        },
        onError: (error) => {
            console.error(error)
        },
    })

    const getInitialFuelLevel = (tank: any) => {
        const fuelLog = fuelLogs
            .filter((log: any) => log.fuelTank.id === tank.id)
            .sort(
                (a: any, b: any) =>
                    new Date(a.date).getTime() - new Date(b.date).getTime(),
            )[0]
        return fuelLog
            ? +tank.capacity > +fuelLog.fuelBefore
                ? +fuelLog.fuelBefore
                : +tank.capacity
            : +tank.currentLevel
    }

    const handleSetCurrentFuelTank = (tank: any) => {
        setCurrentFuelTank({ ...tank, currentLevel: getInitialFuelLevel(tank) })
    }

    const getSafeFuelCapacity = (tank: any) => {
        return +tank.safeFuelCapacity > 0
            ? +tank.safeFuelCapacity
            : +tank.capacity / 2
    }

    const [creteFuelLog] = useMutation(CREATE_FUELLOG, {
        onCompleted: (response) => {
            const data = response.createFuelLog
        },
        onError: (error) => {
            console.error('Error creating fuel log', error)
        },
    })

    const [createFuelReceipts] = useMutation(CREATE_R2FILE, {
        onCompleted: (response) => {
            const data = response.createR2File
            const newReceipts = fuelReceipts.map((receipt: any) => {
                if (receipt.title === data.title) {
                    return {
                        ...receipt,
                        id: data.id,
                    }
                }
                return receipt
            })
            setFuelReceipts(newReceipts)
        },
        onError: (error) => {
            console.error('Error creating fuel receipts', error)
        },
    })

    const [createRefuellingBunkering] = useMutation(CreateRefuellingBunkering, {
        onCompleted: (response) => {
            const data = response.createRefuellingBunkering
            if (fuelReceipts.length > 0) {
                fuelReceipts.map((receipt: any) => {
                    if (!receipt.id && data.id) {
                        createFuelReceipts({
                            variables: {
                                input: {
                                    title: receipt.title,
                                    refuellingBunkeringID: data.id,
                                },
                            },
                        })
                    }
                })
            }
            creteFuelLog({
                variables: {
                    input: {
                        fuelTankID: +currentFuelTank.id,
                        fuelBefore: +currentFuelTank.currentLevel,
                        fuelAdded: +refuelling.fuelAdded,
                        fuelAfter: +refuelling.fuelAfter,
                        costPerLitre: +refuelling.costPerLitre,
                        totalCost: +refuelling.totalCost,
                        date: dayjs(refuelling?.time).format(
                            'YYYY-MM-DDTHH:mm:ss',
                        ),
                        refuellingBunkeringID: data.id,
                    },
                },
            })
            updateFuelTank({
                variables: {
                    input: {
                        id: +currentFuelTank.id,
                        currentLevel: +refuelling.fuelAfter,
                    },
                },
            })
            const tripReportID =
                tripReport.length > 0
                    ? tripReport[tripReport.length - 1].id
                    : null
            setTimeout(() => {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'RefuellingBunkering',
                            logBookEntrySectionID: +tripReportID,
                            eventType_RefuellingBunkeringID: data.id,
                        },
                    },
                })
            }, 200)
            const fuelLog = fuelLogs
                .filter((log: any) => log.fuelTank.id == currentFuelTank.id)
                .sort(
                    (a: any, b: any) =>
                        new Date(a.date).getTime() - new Date(b.date).getTime(),
                )[0]
            updateFuelLog({
                variables: {
                    input: {
                        id: fuelLog.id,
                        fuelAfter: +refuelling.fuelAfter,
                        fuelBefore: +refuelling.fuelAfter,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating refuelling', error)
        },
    })

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setOpenRefuelling(false)
            setRefuelling({
                fuelAdded: 0,
                costPerLitre: 0,
                totalCost: 0,
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const addRefuelling = async () => {
        const tripReportID =
            tripReport.length > 0 ? tripReport[tripReport.length - 1].id : null
        if (tripReportID > 0) {
            createRefuellingBunkering({
                variables: {
                    input: {
                        geoLocationID: currentLocation?.value,
                        notes: refuelling?.content,
                        lat: currentLocation.latitude.toString(),
                        long: currentLocation.longitude.toString(),
                        date: dayjs(refuelling?.time).format(
                            'YYYY-MM-DDTHH:mm:ss',
                        ),
                        tripEventID: tripReportID,
                    },
                },
            })
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), tripReportID],
            })
        } else {
            const input = {
                logBookEntryID: +logentryID,
            }
            createTripReport_LogBookEntrySection({
                variables: {
                    input,
                },
            })
        }
    }

    const [createTripReport_LogBookEntrySection] = useMutation(
        CreateTripReport_LogBookEntrySection,
        {
            onCompleted: (response) => {
                const data = response.createTripReport_LogBookEntrySection
                updateTripReport({
                    id: [...tripReport.map((trip: any) => trip.id), data.id],
                })
                getSectionTripReport_LogBookEntrySection({
                    variables: {
                        id: [data.id],
                    },
                })
                createRefuellingBunkering({
                    variables: {
                        input: {
                            geoLocationID: currentLocation?.value,
                            notes: refuelling?.content,
                            lat: currentLocation.latitude.toString(),
                            long: currentLocation.longitude.toString(),
                            date: dayjs(refuelling?.time).format(
                                'YYYY-MM-DDTHH:mm:ss',
                            ),
                            tripEventID: data.id,
                        },
                    },
                })
            },
            onError: (error) => {
                console.error('Error creating trip report', error)
            },
        },
    )

    const handleLocationChange = (location: any) => {
        setCurrentLocation(location)
    }

    const handleTimeChange = (time: any) => {
        setRefuelling({
            ...refuelling,
            time: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
        })
    }

    const handleEditorChange = (value: string) => {
        setRefuelling({
            ...refuelling,
            content: value,
        })
    }

    const onFuelGaugeClick = (tank: any) => {
        if (+tank.capacity > 0 && +tank.capacity > getSafeFuelCapacity(tank)) {
            setOpenFuelTankDialog(true)
            handleSetCurrentFuelTank(tank)
        } else {
            toast.error('Fuel tank setup is incomplete')
        }
    }

    return (
        <>
            {fuelTankList.length > 0 &&
                fuelTankList.map((tank: any) => (
                    <div
                        key={tank.id}
                        className="cursor-pointer"
                        onClick={() => onFuelGaugeClick(tank)}>
                        <FuelGauge
                            key={tank.id}
                            title={tank.title}
                            currentCapacity={getInitialFuelLevel(tank)}
                            safeCapacity={
                                +tank.capacity > 0 &&
                                +tank.capacity > getSafeFuelCapacity(tank)
                                    ? getSafeFuelCapacity(tank)
                                    : 25
                            }
                            maxCapacity={
                                +tank.capacity > 0 &&
                                +tank.capacity > getSafeFuelCapacity(tank)
                                    ? +tank.capacity
                                    : 100
                            }
                        />
                    </div>
                ))}
            <AlertDialogNew
                openDialog={openFuelTankDialog}
                setOpenDialog={setOpenFuelTankDialog}
                className="max-w-xl"
                handleCreate={handleUpdateFuelTank}
                title="Fuel tank details"
                size="xl"
                secondaryActionText="Record refuelling / bunkering"
                handleAction={() => {
                    setOpenFuelTankDialog(false)
                    setOpenRefuelling(true)
                }}
                // description={currentFuelTank?.title}
                actionText="Save">
                <Label label="Current fuel level" htmlFor="current-fuel">
                    <Input
                        id="current-fuel"
                        type="number"
                        min="0"
                        max={currentFuelTank?.capacity}
                        value={currentFuelTank?.currentLevel}
                        onChange={(e) =>
                            setCurrentFuelTank({
                                ...currentFuelTank,
                                currentLevel: e.target.value,
                            })
                        }
                        className="w-full"
                    />
                </Label>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openRefuelling}
                setOpenDialog={setOpenRefuelling}
                className="max-w-2xl"
                size="xl"
                handleCreate={() => {
                    addRefuelling()
                }}
                title="Record refuelling / bunkering"
                actionText="Save">
                <Label
                    label="Location where refuelling / Bunkering takes place"
                    htmlFor="refuelling-location">
                    <LocationField
                        offline={offline}
                        setCurrentLocation={setCurrentLocation}
                        handleLocationChange={handleLocationChange}
                    />
                </Label>
                <div className="flex flex-wrap">
                    <Label
                        label="Fuel before"
                        htmlFor="fuel-before"
                        className="w-1/2 pr-1">
                        <Input
                            id="fuel-before"
                            type="number"
                            min="0"
                            readOnly
                            max={currentFuelTank?.capacity}
                            value={currentFuelTank?.currentLevel}
                            className="w-full"
                        />
                    </Label>
                    <Label
                        label="Fuel added"
                        htmlFor="fuel-added"
                        className="w-1/2 pl-1">
                        <Input
                            id="fuel-added"
                            type="number"
                            min="0"
                            max={currentFuelTank?.capacity}
                            value={refuelling?.fuelAdded ?? 0}
                            onChange={(e) =>
                                setRefuelling({
                                    ...refuelling,
                                    fuelAdded: e.target.value,
                                    fuelAfter:
                                        +currentFuelTank?.currentLevel +
                                        +e.target.value,
                                    totalCost:
                                        +e.target.value *
                                        +refuelling?.costPerLitre,
                                })
                            }
                            className="w-full"
                        />
                    </Label>
                    <Label
                        label="Fuel after"
                        htmlFor="fuel-after"
                        className="w-1/2 pr-1">
                        <Input
                            id="fuel-after"
                            type="number"
                            min="0"
                            max={currentFuelTank?.capacity}
                            value={
                                refuelling?.fuelAfter ??
                                currentFuelTank?.currentLevel
                            }
                            readOnly
                            className="w-full"
                        />
                    </Label>
                    <Label
                        label="Cost per litre"
                        htmlFor="cost-per-litre"
                        className="w-1/2 pl-1">
                        <Input
                            id="cost-per-litre"
                            type="number"
                            min="0"
                            value={refuelling?.costPerLitre ?? 0}
                            onChange={(e) =>
                                setRefuelling({
                                    ...refuelling,
                                    costPerLitre: e.target.value,
                                    totalCost:
                                        +refuelling?.fuelAdded *
                                        +e.target.value,
                                })
                            }
                            className="w-full"
                        />
                    </Label>
                    <Label
                        label="Total cost"
                        htmlFor="total-cost"
                        className="w-1/2 pr-1">
                        <Input
                            id="total-cost"
                            type="number"
                            min="0"
                            value={refuelling?.totalCost}
                            readOnly
                            className="w-full"
                        />
                    </Label>
                </div>
                <Label label="Refuelling time" htmlFor="fuel-added-time">
                    <TimeField
                        time={
                            refuelling?.time
                                ? dayjs(refuelling?.time).format('HH:mm')
                                : dayjs().format('HH:mm')
                        }
                        handleTimeChange={handleTimeChange}
                        timeID="fuel-added-time"
                        fieldName="Time"
                    />
                </Label>
                <Label label="Fuel Receipts / Comments">
                    <UploadCloudFlare
                        files={fuelReceipts}
                        setFiles={setFuelReceipts}
                    />
                </Label>
                <Label label="Fuel Receipts comment" htmlFor="fuel-receipts">
                    <Textarea
                        id="fuel-receipts"
                        name="fuel-receipts"
                        rows={4}
                        placeholder="Fuel Receipts comment"
                        defaultValue={refuelling?.content}
                        onChange={(e) => handleEditorChange(e.target.value)}
                    />
                </Label>
            </AlertDialogNew>
        </>
    )
}
