!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e||self).nextThemes={},e.react)}(this,function(e,t){function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=/*#__PURE__*/n(t);const a=["light","dark"],r="(prefers-color-scheme: dark)",s="undefined"==typeof window,l=/*#__PURE__*/t.createContext(void 0),c={setTheme:e=>{},themes:[]},i=["light","dark"],d=({forcedTheme:e,disableTransitionOnChange:n=!1,enableSystem:s=!0,enableColorScheme:c=!0,storageKey:d="theme",themes:y=i,defaultTheme:v=(s?"system":"light"),attribute:g="data-theme",value:$,children:b,nonce:p})=>{const[T,S]=t.useState(()=>u(d,v)),[w,C]=t.useState(()=>u(d)),E=$?Object.values($):y,k=t.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=f());const o=$?$[t]:t,r=n?h():null,l=document.documentElement;if("class"===g?(l.classList.remove(...E),o&&l.classList.add(o)):o?l.setAttribute(g,o):l.removeAttribute(g),c){const e=a.includes(v)?v:null,n=a.includes(t)?t:e;l.style.colorScheme=n}null==r||r()},[]),x=t.useCallback(e=>{S(e);try{localStorage.setItem(d,e)}catch(e){}},[e]),L=t.useCallback(t=>{const n=f(t);C(n),"system"===T&&s&&!e&&k("system")},[T,e]);t.useEffect(()=>{const e=window.matchMedia(r);return e.addListener(L),L(e),()=>e.removeListener(L)},[L]),t.useEffect(()=>{const e=e=>{e.key===d&&x(e.newValue||v)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[x]),t.useEffect(()=>{k(null!=e?e:T)},[e,T]);const I=t.useMemo(()=>({theme:T,setTheme:x,forcedTheme:e,resolvedTheme:"system"===T?w:T,themes:s?[...y,"system"]:y,systemTheme:s?w:void 0}),[T,x,e,w,s,y]);/*#__PURE__*/return o.default.createElement(l.Provider,{value:I},/*#__PURE__*/o.default.createElement(m,{forcedTheme:e,disableTransitionOnChange:n,enableSystem:s,enableColorScheme:c,storageKey:d,themes:y,defaultTheme:v,attribute:g,value:$,children:b,attrs:E,nonce:p}),b)},m=/*#__PURE__*/t.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:s,enableColorScheme:l,defaultTheme:c,value:i,attrs:d,nonce:m})=>{const u="system"===c,h="class"===n?`var d=document.documentElement,c=d.classList;c.remove(${d.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${n}',s='setAttribute';`,f=l?a.includes(c)&&c?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${c}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",y=(e,t=!1,o=!0)=>{const r=i?i[e]:e,s=t?e+"|| ''":`'${r}'`;let c="";return l&&o&&!t&&a.includes(e)&&(c+=`d.style.colorScheme = '${e}';`),"class"===n?c+=t||r?`c.add(${s})`:"null":r&&(c+=`d[s](n,${s})`),c},v=e?`!function(){${h}${y(e)}}()`:s?`!function(){try{${h}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${r}',m=window.matchMedia(t);if(m.media!==t||m.matches){${y("dark")}}else{${y("light")}}}else if(e){${i?`var x=${JSON.stringify(i)};`:""}${y(i?"x[e]":"e",!0)}}${u?"":"else{"+y(c,!1,!1)+"}"}${f}}catch(e){}}()`:`!function(){try{${h}var e=localStorage.getItem('${t}');if(e){${i?`var x=${JSON.stringify(i)};`:""}${y(i?"x[e]":"e",!0)}}else{${y(c,!1,!1)};}${f}}catch(t){}}();`;/*#__PURE__*/return o.default.createElement("script",{nonce:m,dangerouslySetInnerHTML:{__html:v}})},()=>!0),u=(e,t)=>{if(s)return;let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},h=()=>{const e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},f=e=>(e||(e=window.matchMedia(r)),e.matches?"dark":"light");e.ThemeProvider=e=>t.useContext(l)?/*#__PURE__*/o.default.createElement(t.Fragment,null,e.children):/*#__PURE__*/o.default.createElement(d,e),e.useTheme=()=>{var e;return null!==(e=t.useContext(l))&&void 0!==e?e:c}});
