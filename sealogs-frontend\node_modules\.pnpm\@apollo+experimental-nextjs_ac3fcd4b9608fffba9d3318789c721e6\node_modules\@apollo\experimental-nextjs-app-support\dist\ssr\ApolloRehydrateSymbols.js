"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApolloBackgroundQueryTransport = exports.ApolloSSRDataTransport = exports.ApolloResultCache = exports.ApolloRehydrationCache = void 0;
exports.ApolloRehydrationCache = Symbol.for("ApolloRehydrationCache");
exports.ApolloResultCache = Symbol.for("ApolloResultCache");
exports.ApolloSSRDataTransport = Symbol.for("ApolloSSRDataTransport");
exports.ApolloBackgroundQueryTransport = Symbol.for("ApolloBackgroundQueryTransport");
//# sourceMappingURL=ApolloRehydrateSymbols.js.map