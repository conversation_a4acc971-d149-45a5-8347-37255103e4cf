{"version": 3, "file": "RehydrationContext.js", "sourceRoot": "", "sources": ["../../src/ssr/RehydrationContext.tsx"], "names": [], "mappings": ";;;;;;AAAA,2CAAiD;AACjD,kDAA0B;AAC1B,iEAA8D;AAC9D,gDAA4D;AAE5D,mDAA2E;AAC3E,gEAAqC;AACrC,+DAA4D;AAE5D,MAAM,wBAAwB,GAAG,eAAK,CAAC,aAAa,CAElD,SAAS,CAAC,CAAC;AAmBN,MAAM,0BAA0B,GAAG,CAAC,EACzC,QAAQ,EACR,gBAAgB,GACiC,EAAE,EAAE;IACrD,MAAM,MAAM,GAAG,IAAA,wBAAe,GAAE,CAAC;IACjC,MAAM,kBAAkB,GAAG,eAAK,CAAC,MAAM,EAA2B,CAAC;IACnE,IAAI,OAAO,MAAM,IAAI,WAAW,EAAE,CAAC;QACjC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAChC,kBAAkB,CAAC,OAAO,GAAG,6BAA6B,CAAC;gBACzD,gBAAgB;aACjB,CAAC,CAAC;QACL,CAAC;QACD,IAAI,MAAM,YAAY,yCAAmB,EAAE,CAAC;YAC1C,MAAM,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;QACJ,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,YAAY,2CAAoB,EAAE,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,8DAA8D,CAC/D,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAA,qCAAqB,GAAE,CAAC;IAC1B,CAAC;IACD,OAAO,CACL,8BAAC,wBAAwB,CAAC,QAAQ,IAAC,KAAK,EAAE,kBAAkB,CAAC,OAAO,IACjE,QAAQ,CACyB,CACrC,CAAC;AACJ,CAAC,CAAC;AAlCW,QAAA,0BAA0B,8BAkCrC;AAEF,SAAgB,qBAAqB;IACnC,MAAM,kBAAkB,GAAG,eAAK,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC;IACtE,MAAM,UAAU,GAAG,eAAK,CAAC,UAAU,CAAC,sCAAyB,CAAC,CAAC;IAE/D,iDAAiD;IACjD,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAO;IAE1C,IACE,UAAU;QACV,kBAAkB;QAClB,CAAC,kBAAkB,CAAC,iBAAiB,EACrC,CAAC;QACD,kBAAkB,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC5C,UAAU,CAAC,GAAG,EAAE,CAAC,8BAAC,kBAAkB,CAAC,iBAAiB,OAAG,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAhBD,sDAgBC;AAED,SAAS,6BAA6B,CAAC,EACrC,gBAAgB,GACQ;IACxB,MAAM,kBAAkB,GAA4B;QAClD,iBAAiB,EAAE,KAAK;QACxB,kBAAkB,EAAE,EAAE;QACtB,iBAAiB,EAAE,EAAE;QACrB,eAAe,EAAE,EAAE;QACnB,yBAAyB,EAAE,EAAE;QAC7B,iBAAiB;YACf,kBAAkB,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAC7C,IACE,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC,MAAM;gBAC1D,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,MAAM;gBACvD,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC,MAAM;gBAEjE,OAAO,6DAAK,CAAC;YACf,sBAAS,CAAC,KAAK,CACb,mBAAmB,EACnB,kBAAkB,CAAC,kBAAkB,CACtC,CAAC;YACF,sBAAS,CAAC,KAAK,CACb,sBAAsB,EACtB,kBAAkB,CAAC,eAAe,CACnC,CAAC;YACF,sBAAS,CAAC,KAAK,CACb,wCAAwC,EACxC,kBAAkB,CAAC,yBAAyB,CAC7C,CAAC;YAEF,MAAM,MAAM,GAAG,IAAA,iCAAiB,EAAC;gBAC/B,SAAS,EAAE,MAAM,CAAC,WAAW,CAC3B,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAC1D,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CACf,kBAAkB,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,KAAK,CACtD,CACF;gBACD,OAAO,EAAE,kBAAkB,CAAC,eAAe;gBAC3C,iBAAiB,EAAE,kBAAkB,CAAC,yBAAyB;aAChE,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CACX,kBAAkB,CAAC,iBAAiB,EACpC,kBAAkB,CAAC,kBAAkB,CACtC,CAAC;YACF,kBAAkB,CAAC,kBAAkB,GAAG,EAAE,CAAC;YAC3C,kBAAkB,CAAC,eAAe,GAAG,EAAE,CAAC;YACxC,kBAAkB,CAAC,yBAAyB,GAAG,EAAE,CAAC;YAClD,OAAO,CACL,0DACM,gBAAgB,IACpB,uBAAuB,EAAE;oBACvB,MAAM;iBACP,IACD,CACH,CAAC;QACJ,CAAC;KACF,CAAC;IACF,OAAO,kBAAkB,CAAC;AAC5B,CAAC"}