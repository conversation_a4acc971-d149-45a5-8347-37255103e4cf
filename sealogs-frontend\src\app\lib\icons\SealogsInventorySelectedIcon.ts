'use client'

import { useTheme } from 'next-themes';
const React = require('react')

export function SealogsInventorySelectedIcon({ ...props }) {
//  return /*#__PURE__*/ React.createElement(
//    'svg',
//    Object.assign(
//      {
//        className: cn('p-0.5 h-6 w-6', className),
//        xmlns: 'http://www.w3.org/2000/svg',
//        viewBox: '0 0 119.1 122.83',
//      },
//      props,
//    ),
//    /*#__PURE__*/ React.createElement('path', {
//      d: 'M95.41,53.45l8.9-44.17c.04-.21-.07-.41-.26-.49L85.61.76h-.05s-.04,0-.06,0c-.1-.02-.2,0-.28.04h-.03c-.25.15-10.5,6.04-25.62,6.04S34.21.94,33.95.79h-.03c-.09-.05-.18-.06-.28-.04-.02,0-.04,0-.07,0h-.05L15.07,8.79c-.19.09-.3.29-.26.49l9.17,46.07L.93,73.79c-.11.09-.17.22-.17.35v47.42c0,.25.2.44.45.44h50.22c.12,0,.24-.05.32-.13l7.8-7.71,7.8,7.71c.08.08.2.13.32.13h50.22c.25,0,.47-.18.47-.43l-.02-47.44c0-.14-.06-.27-.17-.35l-23.04-18.44h0l.29-1.9ZM60.77,90.78l-.83-46.49c2.93-1.03,24.41-9.81,25.22-42.34.02-.64.68-1.08,1.28-.82l17.96,7.75-9.12,45.69c-.03.16.2,1.03.33,1.13l22.63,18.33v18.85M59.57,7.73c13.25,0,22.84-4.47,25.4-5.8-.35,33.43-23.49,41.91-25.4,42.55-1.9-.63-25.04-9.09-25.4-42.55,2.56,1.32,12.15,5.8,25.4,5.8h0ZM.9,92.97v-19.09l23.2-18.58c.13-.11.04-.22.01-.39L14.59,8.93,33.55,1.34c.31,33.43,23.34,42.23,26.38,43.28l.77,45.2M1.22,93.09l45.33.16-.12,10.86-45.68.12v-11.22l.47.09ZM59.53,114.3l-8.22,7.48-49.26-.18c-.72,0-1.3-.57-1.3-1.28l.15-16.24,44.87-.08c.22,1.58,1.59,3.3,3.26,3.3l10.75.33.08,5.47s-.33,1.21-.33,1.21ZM49.01,106.82c-1.32,0-2.39-1.06-2.39-2.36v-11.73c0-1.3,1.07-2.37,2.39-2.37h21.1c1.32,0,2.39,1.07,2.39,2.37v11.73c0,1.3-1.07,2.36-2.39,2.36h-21.11,0ZM117.77,121.95l-49.23.13c-.6,0-1.19-.23-1.61-.65l-7.08-7.21v-7.08l9.93.08c1.66,0,3.12-1.72,3.34-3.3l45.18-.13.03,17.55-.11.38-.45.24ZM117.88,103.87l-44.83-.12-.24-10.5,45.07.12v10.5Z',
//      fill: '#fff',
//      stroke: '#022450',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//    /*#__PURE__*/ React.createElement('path', {
//      d: 'M34.03,11.78l-17.35,7.9',
//      fill: '#fff',
//      stroke: '#022450',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//    /*#__PURE__*/ React.createElement('path', {
//      d: 'M17.72,25.44l18.49-8.42',
//      fill: '#fff',
//      stroke: '#022450',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//    /*#__PURE__*/ React.createElement('path', {
//      d: 'M102.01,19.86l-17.85-5.8',
//      fill: '#fff',
//      stroke: '#022450',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//    /*#__PURE__*/ React.createElement('path', {
//      d: 'M82.3,17.96l18.66,6.05',
//      fill: '#fff',
//      stroke: '#022450',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//  );
  const { theme, setTheme } = useTheme()

  const getTheme = () => {
    switch (theme) {
      case 'dark':
        return 'Dark'
      case 'light':
        return 'Light'
      default:
        return 'System'
    }
  }

  getTheme()

  if (theme === 'light') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/lightjacket-selected-light.svg',
      alt: 'Lifejacket Light Icon',
      ...props,
    });
  } else if (theme === 'dark') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/lifejacket-selected-dark.svg',
      alt: 'Lifejacket Dark Icon',
      ...props,
    });
  }
}

