import { useFragment as orig_useFragment, useSuspenseQuery as orig_useSuspenseQuery, useReadQuery as orig_useReadQuery, useQuery as orig_useQuery, useBackgroundQuery as orig_useBackgroundQuery } from "@apollo/client";
export declare const useFragment: typeof orig_useFragment;
export declare const useQuery: typeof orig_useQuery;
export declare const useSuspenseQuery: typeof orig_useSuspenseQuery;
export declare const useReadQuery: typeof orig_useReadQuery;
export declare const useBackgroundQuery: typeof orig_useBackgroundQuery;
//# sourceMappingURL=hooks.d.ts.map