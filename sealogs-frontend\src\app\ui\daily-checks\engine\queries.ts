import gql from 'graphql-tag'

// GET_SECTION_MEMBER_COMMENTS
export const ReadSectionMemberComments = gql`
    query ReadSectionMemberComments(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SectionMemberCommentFilterFields = {}
    ) {
        readSectionMemberComments(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                commentType
                fieldName
                comment
                logBookEntrySectionID
                hideComment
            }
        }
    }
`
// GET_ENGINES
export const ReadEngines = gql`
    query ReadEngines($id: [ID]!, $filter: EngineStartStopFilterFields = {}) {
        readEngines(filter: { id: { in: $id } }) {
            nodes {
                id
                title
                type
                currentHours
                identifier
                isPrimary
                maxPower
                driveType
                positionOnVessel
                archived
                componentCategory
                make
                model
                kW
                kVA
                engineStartStops(sort: { created: ASC }, filter: $filter) {
                    nodes {
                        id
                        hoursStart
                        hoursEnd
                        totalHours
                        timeStart
                        timeEnd
                        logBookEntrySection {
                            logBookEntryID
                        }
                    }
                }
            }
        }
    }
`

// GET_FUELTANKS
export const ReadFuelTanks = gql`
    query ReadFuelTanks($id: [ID]!) {
        readFuelTanks(filter: { id: { in: $id } }) {
            nodes {
                id
                title
                identifier
                archived
                componentCategory
                capacity
                safeFuelCapacity
                currentLevel
                lastEdited
                fuelType
                dipType
                dipConversions
                dipImportID
                dipImportRun
                fuelTankStartStops {
                    nodes {
                        id
                        engineID
                    }
                }
            }
        }
    }
`

// GET_LOGBOOK_ENTRY_BY_ID
export const ReadOneLogBookEntry = gql`
    query ReadOneLogBookEntry($logbookEntryId: ID!) {
        readOneLogBookEntry(filter: { id: { eq: $logbookEntryId } }) {
            id
            masterID
            state
            className
            startDate
            endDate
            fuelLevel
            logBookID
            createdByID
            signOffCommentID
            signOffSignatureID
            clientID
            lockedDate
            lastConfig
            fuelLog {
                nodes {
                    id
                    fuelAdded
                    fuelBefore
                    fuelAfter
                    date
                    costPerLitre
                    totalCost
                    fuelTank {
                        id
                        capacity
                        safeFuelCapacity
                        currentLevel
                        title
                    }
                }
            }
            engineStartStop {
                nodes {
                    id
                    hoursStart
                    engineID
                    engine {
                        id
                        title
                        currentHours
                    }
                }
            }
            logBook {
                id
                title
                componentConfig
            }
            master {
                id
                firstName
                surname
            }
            logBookEntrySections {
                nodes {
                    id
                    className
                }
            }
            vehicle {
                id
                seaLogsMembers {
                    nodes {
                        id
                        firstName
                        surname
                        archived
                        primaryDutyID
                    }
                }
            }
        }
    }
`

// LogBookSignOff_LogBookEntrySection
export const ReadLogBookSignOff_LogBookEntrySections = gql`
    query ReadLogBookSignOff_LogBookEntrySections($id: [ID]!) {
        readLogBookSignOff_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                review
                safetyEquipmentCheck
                forecastAccuracy
                ais
                created
                navigationLightsAndShapes
                electronicNavigationalAids
                mainEngines
                auxiliarySystems
                fuelAndOil
                bilgeSystems
                power
                batteryMaintenance
                circuitInspections
                mooringAndAnchoring
                cargoAndAccessEquipment
                hatchesAndWatertightDoors
                galleyAppliances
                wasteManagement
                ventilationAndAirConditioning
                emergencyReadiness
                environmentalCompliance
                navigationAndBridgeEquipment
                engineRoomAndMachinery
                electricalSystems
                deckOperations
                accommodationAndGalley
                finalChecks
                signOffMemberID
                fuelStart
                sectionSignatureID
                completedTime
                endLocationID
                endLocation {
                    id
                    lat
                    long
                    time
                    geoLocationID
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
                sectionSignature {
                    id
                    signatureData
                }
            }
        }
    }
`

// TripReport_LogBookEntrySection
export const ReadTripReport_LogBookEntrySections = gql`
    query ReadTripReport_LogBookEntrySections($id: [ID]!) {
        readTripReport_LogBookEntrySections(
            filter: { id: { in: $id } }
            sort: { created: ASC }
        ) {
            nodes {
                id
                archived
                sectionSignatureID
                sectionSignature {
                    id
                    signatureData
                }
                createdByID
                dailyChecksCompleted
                depart
                departFrom
                arrive
                lastEdited
                totalPOB
                totalGuests
                totalPaxJoined
                totalVehiclesJoined
                arriveTo
                departTime
                tripScheduleDepartTime
                fromFreehand
                fromLat
                fromLong
                arriveTime
                tripScheduleArriveTime
                toFreehand
                toLat
                toLong
                pob
                numberPax
                paxJoinedAdult
                paxJoinedChild
                paxJoinedYouth
                paxJoinedFOC
                paxJoinedStaff
                paxJoinedVoucher
                paxJoinedPrePaid
                paxDeparted
                safetyBriefing
                speedExemption
                expectedNextContact
                fromCreatesNewGeoLocation
                toCreatesNewGeoLocation
                voucher
                incidentReports
                hazardReports
                prevPaxState
                comment
                vob
                totalVehiclesCarried
                vehiclesJoined
                vehiclesDeparted
                observedDepart
                observedArrive
                masterID
                leadGuideID
                fromLocationID
                toLocationID
                tripReportScheduleID
                tripReportSchedule {
                    id
                    tripScheduleServiceID
                }
                lateDepartureReasonID
                tripUpdateEntityID
                speedExemptionCorridorID
                speedExemptionReasonID
                unscheduledServiceID
                designatedDangerousGoodsSailing
                dangerousGoodsRecords {
                    nodes {
                        id
                        comment
                        type
                    }
                }
                enableDGR
                dangerousGoodsChecklist {
                    id
                    vesselSecuredToWharf
                    bravoFlagRaised
                    twoCrewLoadingVessel
                    fireHosesRiggedAndReady
                    noSmokingSignagePosted
                    spillKitAvailable
                    fireExtinguishersAvailable
                    dgDeclarationReceived
                    loadPlanReceived
                    msdsAvailable
                    anyVehiclesSecureToVehicleDeck
                    safetyAnnouncement
                    vehicleStationaryAndSecure
                    riskFactors {
                        nodes {
                            id
                            type
                            title
                            impact
                            probability
                            created
                            mitigationStrategy {
                                nodes {
                                    id
                                    strategy
                                }
                            }
                        }
                    }
                }
                tripEvents(sort: { created: ASC }) {
                    nodes {
                        id
                        created
                        start
                        end
                        numberPax
                        cause
                        notes
                        location
                        vehicle
                        eventCategory
                        eventType_VesselRescueID
                        eventType_PersonRescueID
                        eventType_BarCrossingID
                        eventType_RestrictedVisibilityID
                        eventType_PassengerDropFacilityID
                        eventType_TaskingID
                        incidentRecordID
                        eventType {
                            id
                            title
                        }
                        eventType_PassengerDropFacility {
                            id
                            title
                            fuelLevel
                            time
                            type
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        eventType_Tasking {
                            id
                            title
                            time
                            type
                            status
                            cgop
                            sarop
                            fuelLevel
                            tripEventID
                            pausedTaskID
                            openTaskID
                            completedTaskID
                            vesselRescueID
                            personRescueID
                            groupID
                            parentTaskingID
                            towingChecklist {
                                id
                            }
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        eventType_BarCrossing {
                            id
                            time
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            timeCompleted
                            geoLocationCompleted {
                                id
                                title
                                lat
                                long
                            }
                            barCrossingChecklist {
                                id
                            }
                        }
                        eventType_RestrictedVisibility {
                            id
                            crossingTime
                            crossedTime
                            startLocationID
                            startLocation {
                                title
                            }
                            endLocationID
                        }
                        tripUpdate {
                            id
                            date
                            title
                            lat
                            long
                            notes
                            attachment {
                                nodes {
                                    id
                                    title
                                }
                            }
                            geoLocationID
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                        }
                        eventType_RefuellingBunkering {
                            id
                            date
                            title
                            lat
                            long
                            notes
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            geoLocationID
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        supernumerary {
                            id
                            title
                            totalGuest
                            focGuest
                            isBriefed
                            briefingTime
                            guestList {
                                nodes {
                                    id
                                    firstName
                                    surname
                                    sectionSignature {
                                        id
                                        signatureData
                                    }
                                }
                            }
                        }
                        crewTraining {
                            id
                            startTime
                            finishTime
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                        }
                        infringementNotice {
                            id
                            time
                            vesselType
                            vesselName
                            vesselReg
                            ownerFullName
                            ownerAddress
                            ownerPhone
                            ownerEmail
                            ownerDOB
                            ownerOccupation
                            infringementData
                            otherDescription
                            waterwaysOfficerID
                            geoLocationID
                            signatureID
                            lat
                            long
                            waterwaysOfficer {
                                id
                                firstName
                                surname
                            }
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            signature {
                                id
                                signatureData
                            }
                        }
                        incidentRecord {
                            id
                            title
                            startDate
                            endDate
                            incidentType
                            personsInvolved
                            description
                            treatment
                            contributingFactor
                            riskAssessmentReviewed
                            notifiable
                            location {
                                id
                                title
                                lat
                                long
                            }
                            reportedBy {
                                id
                                firstName
                                surname
                            }
                            vessel {
                                id
                                title
                            }
                        }
                    }
                }
                tripReport_Stops {
                    nodes {
                        id
                        arriveTime
                        departTime
                        paxJoined
                        paxDeparted
                        vehiclesJoined
                        vehiclesDeparted
                        stopLocationID
                        otherCargo
                        lat
                        long
                        comments
                        designatedDangerousGoodsSailing
                        dangerousGoodsChecklistID
                        stopLocation {
                            id
                            title
                            lat
                            long
                        }
                    }
                }
                fromLocation {
                    id
                    title
                    lat
                    long
                }
                toLocation {
                    id
                    title
                    lat
                    long
                }
            }
        }
    }
`

// VesselDailyCheck_LogBookEntrySection
export const ReadVesselDailyCheck_LogBookEntrySections = gql`
    query ReadVesselDailyCheck_LogBookEntrySections($id: [ID]!) {
        readVesselDailyCheck_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                checkTime
                bilgeLevels
                bilgePumps
                hull
                navEquipment
                oilAndWater
                engineRoomChecks
                forwardAndReverseBelts
                driveShafts
                steeringTiller
                cablesFRPullies
                throttleAndCable
                wiring
                beltsHosesClamps
                sandTraps
                batteries
                safetyEquipment
                checksWithManual
                cabin
                preStartupChecks
                engineChecks
                otherNavigation
                navigationCharts
                engineCheckPropellers
                forwardReverse
                electricalVisualFields
                postElectricalStrainers
                engineOilWater
                engineMountsAndStabilisers
                postStartupEngineChecks
                preEngineAndPropulsion
                postEngineAndPropulsion
                postElectrical
                otherEngineFields
                preFuelLevelStart
                preFuelLevelEnd
                houseBatteriesStatus
                checkOilPressure
                batteryIsCharging
                shorePowerIsDisconnected
                lockToLockSteering
                trimTabs
                steeringTrimTabs
                oilWater
                electrical
                postStartupChecks
                navigationChecks
                depthSounder
                radar
                tracPlus
                chartPlotter
                sart
                aisOperational
                vhf
                uhf
                forwardAndReverse
                hull_HullStructure
                pontoonPressure
                bungsInPlace
                hull_DeckEquipment
                swimPlatformLadder
                biminiTopsCanvasCovers
                windscreenCheck
                nightLineDockLinesRelease
                floor
                engineMounts
                engineOil
                engineTellTale
                engineIsFit
                steeringFluid
                steeringRams
                steeringIsFit
                epirb
                lifeJackets
                fireExtinguisher
                unitTransomBolts
                cotterPins
                reverseBucketAndRam
                nozzleAndBearings
                tailHousing
                weatherSummary
                windDirection
                windStrength
                swell
                lifeRings
                flares
                fireHoses
                fireBuckets
                fireBlanket
                fireAxes
                firePump
                fireFlaps
                lifeRaft
                highWaterAlarm
                firstAid
                exterior
                interior
                cleanGalleyBench
                cleanGalleyFloor
                cleanTable
                cleanMirrorGlass
                cleanToilet
                cleanSink
                cleanDeckFloor
                cleanOutsideWallWindow
                cleanGarbageBin
                cleanBoothSeat
                cleanFridge
                cleanCupboard
                cleanOven
                cleanSouvenir
                cleanRestockSalesItem
                cleanTill
                charts
                documentCrewBriefings
                recordComments
                engineChecks
                steering
                cooling
                propulsion
                bilge
                engineRoom
                throttle
                jetUnit
                generator
                fuelLevel
                fuelTanks
                fuelFilters
                fuel
                hullStructure
                deckEquipment
                anchor
                hatches
                dayShapes
                hvac
                tv
                stabilizationSystems
                electronics
                gps
                radio
                navigationLights
                compass
                soundSignallingDevices
                navigationHazards
                wheelhouse
                bilgeCheck
                sewage
                freshWater
                sanitation
                pestControl
                mainEngine
                transmission
                steeringPropultion
                propulsionCheck
                stabilizers
                exhaust
                propulsionBatteriesStatus
                personOverboardRescueEquipment
                smokeDetectors
                shorePower
                electricalPanels
                seaStrainers
                sail
                mainEngineChecks
                propulsionEngineChecks
                propulsionPropulsion
                electricalChecks
                engineRoomVisualInspection
                fuelSystems
                steeringChecks
                throttleAndCableChecks
                tenderOperationalChecks
                airShutoffs
                fireDampeners
                coolantLevels
                fuelShutoffs
                separators
                steeringRudders
                steeringHoses
                steeringTillers
                steeringHydraulicSystems
                operationalTestsOfHelms
                driveShaftsChecks
                gearBox
                propellers
                skeg
                engrMechanical
                mechCrankcaseOilLevel
                mechCoolingWaterLevel
                mechTransmissionOilLevel
                mechInspectPipework
                mechHydraulicSteeringOilLevel
                mechGearBoxOilLevel
                mechInspectVeeBelts
                engrGenerator
                genCrankcaseOilLevel
                genCoolingWaterLevel
                genElectrical
                genPracxisSystemOperative
                genTest24VLighting
                genRunningTankFuelLevel
                engrElectronics
                electrDeckLights
                electrSearchLights
                electrChart
                engrTowlineWinch
                towCheckWinchCondition
                towProveWinchOperation
                towSelectControlStation
                towCheckTowlineCondition
                biosecGlueBoardTraps
                crewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                preCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                preCheckTime
                postCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                postCheckTime
                otherEngineCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                otherEngineCheckTime
                deckOpsCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                deckOpsCheckTime
                navigationCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                navigationCheckTime
                jetCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                jetCheckTime
                cleaningCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                cleaningCheckTime
                hvacCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                hvacCheckTime
                plumbingCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                plumbingCheckTime
                sailCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                sailCheckTime
                engrCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                engrCheckTime
                biosecCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                biosecCheckTime
            }
        }
    }
`

// VESSEL_INFO
export const ReadOneVessel = gql`
    query ReadOneVessel($id: ID!) {
        readOneVessel(filter: { id: { eq: $id } }) {
            id
            title
            registration
            logBookID
            seaLogsMembers {
                nodes {
                    id
                    archived
                    firstName
                    surname
                }
            }
            statusHistory(sort: { created: DESC }, limit: 1) {
                nodes {
                    id
                    date
                    status
                    comment
                    reason
                    otherReason
                    expectedReturn
                }
            }
            minCrew
            maxPax
            maxPOB
            activities
            numberOfEngines
            numberOfShafts
            sharedFuelTank
            documents {
                nodes {
                    id
                    fileFilename
                    name
                    title
                    created
                }
            }
            R2Documents {
                nodes {
                    id
                    title
                }
            }
            vehiclePositions(sort: { created: DESC }, limit: 1) {
                nodes {
                    id
                    lat
                    long
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
            }
            icon
            metServiceObsLocation
            metServiceForecastLocation
            enableTransitMessaging
            copyCrewToOtherActivites
            templateVisible
            mmsi
            iconMode
            callSign
            countryOfOperation
            showOnDashboard
            displayLogbookComments
            transitID
            vesselSpecifics {
                id
                primaryHarbour
                overallLength
                beam
                draft
                dateOfBuild
                hullColor
                hullConstruction
                maxCargoLoad
                operatingAreaLimits
                specialLimitations
                portOfRegistry
                fishingNumber
                loadLineLength
                registeredLength
                tonnageLength
                grossTonnage
                netTonnage
                capacityOfLifting
                carriesDangerousGoods
                carriesVehicles
                designApprovalNumber
            }
            identifier
            archived
            vesselType
            vesselTypeDescription
            vesselSpecificsID
            photoID
            photo {
                id
                fileFilename
                title
                created
            }
            currentTripServiceID
            clientID
            bannerImageID
            componentMaintenanceSchedules {
                nodes {
                    id
                }
            }
            parentComponent_Components {
                nodes {
                    basicComponent {
                        id
                        title
                        componentCategory
                    }
                    parentComponent {
                        id
                        title
                    }
                }
            }
            logBookEntries(sort: { id: DESC }) {
                nodes {
                    id
                    state
                }
            }
            departments {
                nodes {
                    id
                    title
                }
            }
            defaultRadioLogs {
                nodes {
                    id
                    title
                    status
                    comment
                    time
                    vesselID
                    logBookEntryID
                }
            }
        }
    }
`
