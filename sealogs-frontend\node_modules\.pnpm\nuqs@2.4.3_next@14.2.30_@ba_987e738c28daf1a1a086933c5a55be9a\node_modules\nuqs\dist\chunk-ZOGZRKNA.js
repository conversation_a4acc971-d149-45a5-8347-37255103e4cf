import { debug, renderQueryString } from './chunk-5WWTJYGR.js';
import { useRouter, useSearchParams } from 'next/navigation.js';
import { useOptimistic, useCallback, startTransition } from 'react';

function useNuqsNextAppRouterAdapter() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [optimisticSearchParams, setOptimisticSearchParams] = useOptimistic(searchParams);
  const updateUrl = useCallback((search, options) => {
    startTransition(() => {
      if (!options.shallow) {
        setOptimisticSearchParams(search);
      }
      const url = renderURL(location.origin + location.pathname, search);
      debug("[nuqs queue (app)] Updating url: %s", url);
      const updateMethod = options.history === "push" ? history.pushState : history.replaceState;
      updateMethod.call(
        history,
        // In next@14.1.0, useSearchParams becomes reactive to shallow updates,
        // but only if passing `null` as the history state.
        null,
        "",
        url
      );
      if (options.scroll) {
        window.scrollTo(0, 0);
      }
      if (!options.shallow) {
        router.replace(url, {
          scroll: false
        });
      }
    });
  }, []);
  return {
    searchParams: optimisticSearchParams,
    updateUrl,
    // See: https://github.com/47ng/nuqs/issues/603#issuecomment-2317057128
    // and https://github.com/47ng/nuqs/discussions/960#discussioncomment-12699171
    rateLimitFactor: 3
  };
}
function renderURL(base, search) {
  const hashlessBase = base.split("#")[0] ?? "";
  const query = renderQueryString(search);
  const hash = location.hash;
  return hashlessBase + query + hash;
}

export { useNuqsNextAppRouterAdapter };
