'use client'

import { Textarea } from '@/components/ui/textarea'
import VesselFuelStatus from '../../../../vessels/logbookFuelStatus/logbookFuelStatus'
import { Dispatch, useRef } from 'react'
import FuelTankModel from '@/app/offline/models/fuelTank'
import { useLazyQuery } from '@apollo/client'
import { H4 } from '@/components/ui'
import { useDebounceFn } from '@reactuses/core'
import { ReadFuelLogs } from './queries'

interface IProps {
    offline?: boolean
    locked: boolean
    updateTripReport?: any
    loaded: boolean
    vesselDailyCheck: any
    edit_logBookEntry: boolean
    fuelTankList: any
    setFuelTankList: Dispatch<any>
    fuelLogs: any
    setFuelLogs: Dispatch<any>
    getFuelTanks: Function
    setSectionFuelComment: Dispatch<any>
    getComment: (fieldName: string, commentType?: string) => any
    updateSectionMemberComment: Function
    createSectionMemberComment: Function
}

export default function FuelLevel({
    offline,
    locked,
    updateTripReport,
    edit_logBookEntry,
    vesselDailyCheck,
    fuelTankList,
    setFuelTankList,
    loaded,
    getFuelTanks,
    fuelLogs,
    setFuelLogs,
    setSectionFuelComment,
    getComment,
    updateSectionMemberComment,
    createSectionMemberComment,
}: IProps) {
    const fuelTankModel = new FuelTankModel()
    const commentRef = useRef<HTMLTextAreaElement | null>(null)

    const getFuelLogs = async (fuelLogIds: any) => {
        if (offline) {
            const data = await fuelTankModel.getByIds(fuelLogIds)
            setFuelTankList(data)
        } else {
            await queryGetFuelLogs({
                variables: {
                    id: fuelLogIds,
                },
            })
        }
    }

    const [queryGetFuelLogs] = useLazyQuery(ReadFuelLogs, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelLogs.nodes
            setFuelLogs(data)
        },
        onError: (error: any) => {
            console.error('getFuelLogs error', error)
        },
    })

    const saveSectionComment = () => {
        getComment('DailyCheckFuel', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('DailyCheckFuel', 'Section').id,
                          comment: commentRef.current?.value,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'DailyCheckFuel',
                          comment: commentRef.current?.value,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)

    return (
        <>
            <H4>Fuel levels</H4>
            <div
                className={`flex flex-wrap ${locked || !edit_logBookEntry ? 'pointer-events-none opacity-60' : ''}`}>
                {fuelTankList && loaded && (
                    <VesselFuelStatus
                        offline={offline}
                        updateTripReport={updateTripReport}
                        fuelTankList={fuelTankList}
                        updateFuelLogList={getFuelLogs}
                        updateFuelTankList={getFuelTanks}
                        fuelLogs={fuelLogs}
                    />
                )}
            </div>

            <p className="text-sm leading-loose mt-2 mb-4">
                Click the gauge to change the fuel level.
            </p>
            <div className="my-4">
                <Textarea
                    id={`section_comment`}
                    placeholder="Comment if the fuel start value differs from the fuel end value in the previous logbook entry."
                    disabled={locked || !edit_logBookEntry}
                    rows={4}
                    className={`${''} mt-4`}
                    ref={commentRef}
                    onChange={(e) => {
                        setSectionFuelComment(e.target.value)
                        debounceSaveSectionComment()
                    }}
                    // onBlur={(e) => {

                    // }}
                    defaultValue={
                        getComment('DailyCheckFuel', 'Section')?.comment
                    }></Textarea>
            </div>
        </>
    )
}
