import React from "react";
import type { RehydrationContextValue } from "./types";
export interface HydrationContextOptions {
    extraScriptProps?: ScriptProps;
}
type SerializableProps<T> = Pick<T, {
    [K in keyof T]: T[K] extends string | number | boolean | undefined | null ? K : never;
}[keyof T]>;
type ScriptProps = SerializableProps<React.ScriptHTMLAttributes<HTMLScriptElement>>;
export declare const RehydrationContextProvider: ({ children, extraScriptProps, }: React.PropsWithChildren<HydrationContextOptions>) => React.JSX.Element;
export declare function useRehydrationContext(): RehydrationContextValue | undefined;
export {};
//# sourceMappingURL=RehydrationContext.d.ts.map