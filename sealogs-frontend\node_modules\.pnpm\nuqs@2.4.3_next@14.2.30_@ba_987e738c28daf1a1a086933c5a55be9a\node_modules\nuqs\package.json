{"name": "nuqs", "version": "2.4.3", "description": "Type-safe search params state manager for React - Like useState, but stored in the URL query string", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://francoisbest.com"}, "funding": "https://github.com/sponsors/franky47", "repository": {"type": "git", "url": "git+https://github.com/47ng/nuqs.git", "directory": "packages/nuqs"}, "homepage": "https://nuqs.47ng.com", "keywords": ["url state", "url", "querystring", "query string", "search params", "next-usequerystate", "useQueryState", "useQueryStates", "nextjs", "react", "remix", "react-router"], "publishConfig": {"access": "public", "provenance": true}, "files": ["dist/", "server.d.ts", "testing.d.ts", "adapters/react.d.ts", "adapters/next.d.ts", "adapters/next/app.d.ts", "adapters/next/pages.d.ts", "adapters/remix.d.ts", "adapters/react-router.d.ts", "adapters/react-router/v6.d.ts", "adapters/react-router/v7.d.ts", "adapters/custom.d.ts", "adapters/testing.d.ts", "esm-only.cjs"], "type": "module", "sideEffects": false, "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./esm-only.cjs"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.js", "require": "./esm-only.cjs"}, "./testing": {"types": "./dist/testing.d.ts", "import": "./dist/testing.js", "require": "./esm-only.cjs"}, "./adapters/react": {"types": "./dist/adapters/react.d.ts", "import": "./dist/adapters/react.js", "require": "./esm-only.cjs"}, "./adapters/next": {"types": "./dist/adapters/next.d.ts", "import": "./dist/adapters/next.js", "require": "./esm-only.cjs"}, "./adapters/next/app": {"types": "./dist/adapters/next/app.d.ts", "import": "./dist/adapters/next/app.js", "require": "./esm-only.cjs"}, "./adapters/next/pages": {"types": "./dist/adapters/next/pages.d.ts", "import": "./dist/adapters/next/pages.js", "require": "./esm-only.cjs"}, "./adapters/remix": {"types": "./dist/adapters/remix.d.ts", "import": "./dist/adapters/remix.js", "require": "./esm-only.cjs"}, "./adapters/react-router": {"types": "./dist/adapters/react-router.d.ts", "import": "./dist/adapters/react-router.js", "require": "./esm-only.cjs"}, "./adapters/react-router/v6": {"types": "./dist/adapters/react-router/v6.d.ts", "import": "./dist/adapters/react-router/v6.js", "require": "./esm-only.cjs"}, "./adapters/react-router/v7": {"types": "./dist/adapters/react-router/v7.d.ts", "import": "./dist/adapters/react-router/v7.js", "require": "./esm-only.cjs"}, "./adapters/custom": {"types": "./dist/adapters/custom.d.ts", "import": "./dist/adapters/custom.js", "require": "./esm-only.cjs"}, "./adapters/testing": {"types": "./dist/adapters/testing.d.ts", "import": "./dist/adapters/testing.js", "require": "./esm-only.cjs"}}, "scripts": {"dev": "tsup --watch", "prebuild": "rm -rf dist", "build": "tsup", "postbuild": "size-limit --json > size.json", "test": "pnpm run --stream '/^test:/'", "test:unit": "vitest run --typecheck", "test:size": "size-limit", "prepack": "./scripts/prepack.sh"}, "dependencies": {"mitt": "^3.0.1"}, "peerDependencies": {"@remix-run/react": ">=2", "next": ">=14.2.0", "react": ">=18.2.0 || ^19.0.0-0", "react-router": "^6 || ^7", "react-router-dom": "^6 || ^7"}, "peerDependenciesMeta": {"@remix-run/react": {"optional": true}, "next": {"optional": true}, "react-router": {"optional": true}, "react-router-dom": {"optional": true}}, "devDependencies": {"@microsoft/api-extractor": "7.52.3", "@remix-run/react": "^2.16.5", "@size-limit/preset-small-lib": "^11.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.14.1", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.1.1", "fast-check": "^4.1.1", "jsdom": "^26.1.0", "next": "15.3.0", "react": "catalog:react19", "react-dom": "catalog:react19", "react-router-dom": "6.30.0", "size-limit": "^11.2.0", "tsup": "^8.4.0", "typescript": "^5.8.3", "vitest": "^3.1.1", "vitest-package-exports": "^0.1.1"}, "size-limit": [{"name": "Client", "path": "dist/index.js", "limit": "5 kB", "ignore": ["react", "next"]}, {"name": "Server", "path": "dist/server.js", "limit": "2.5 kB", "ignore": ["react", "next"]}]}