{"version": 3, "file": "dataTransport.js", "sourceRoot": "", "sources": ["../../src/ssr/dataTransport.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAAkC;AAClC,qEAKkC;AAElC,mEAAwE;AAExE,gEAAqC;AACrC,mDAA0D;AAU1D;;GAEG;AACH,SAAgB,iBAAiB,CAAC,IAAqB;IACrD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,+CAAsB,CAAC,CAAC;IAClD,OAAO,uBAAuB,GAAG,oBAAoB,IAAA,iCAAoB,EACvE,mBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAC1B,GAAG,CAAC;AACP,CAAC;AALD,8CAKC;AAED;;;GAGG;AACH,SAAgB,qBAAqB;IACnC,IAAA,qDAA6B,EAAC,+CAAsB,EAAE,CAAC,IAAI,EAAE,EAAE;;QAC7D,MAAM,MAAM,GAAG,mBAAS,CAAC,WAAW,CAAkB,IAAI,CAAC,CAAC;QAC5D,sBAAS,CAAC,KAAK,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAC;QAC1D,MAAM,CAAC,MAAM,CAAC,OAAC,MAAM,CAAC,+CAAsB,qCAA7B,MAAM,CAAC,+CAAsB,IAAM,EAAE,EAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACzE,OAAC,MAAM,CAAC,uDAA8B,qCAArC,MAAM,CAAC,uDAA8B,IAAM,EAAE,EAAC,CAAC,IAAI,CAClD,GAAG,MAAM,CAAC,iBAAiB,CAC5B,CAAC;QACF,OAAC,MAAM,CAAC,0CAAiB,qCAAxB,MAAM,CAAC,0CAAiB,IAAM,EAAE,EAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC;AAVD,sDAUC"}