import type { InMemoryCacheConfig, Cache, Reference } from "@apollo/client";
import { InMemoryCache } from "@apollo/client";
import type { RehydrationContextValue } from "./types";
export declare class NextSSRInMemoryCache extends InMemoryCache {
    private rehydrationContext;
    constructor(config?: InMemoryCacheConfig);
    write(options: Cache.WriteOptions<any, any>): Reference | undefined;
    setRehydrationContext(rehydrationContext: RehydrationContextValue): void;
}
//# sourceMappingURL=NextSSRInMemoryCache.d.ts.map