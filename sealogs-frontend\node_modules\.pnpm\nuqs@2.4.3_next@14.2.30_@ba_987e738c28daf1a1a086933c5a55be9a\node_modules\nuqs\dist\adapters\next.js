'use client';

import { useNuqsNextAppRouterAdapter } from '../chunk-ZOGZRKNA.js';
import { useNuqsNextPagesRouterAdapter, isPagesRouter } from '../chunk-6TJO5LV6.js';
import { createAdapterProvider } from '../chunk-5WWTJYGR.js';

// src/adapters/next.ts
function useNuqsNextAdapter() {
  const pagesRouterImpl = useNuqsNextPagesRouterAdapter();
  const appRouterImpl = useNuqsNextAppRouterAdapter();
  return {
    searchParams: appRouterImpl.searchParams,
    updateUrl(search, options) {
      if (isPagesRouter()) {
        return pagesRouterImpl.updateUrl(search, options);
      } else {
        return appRouterImpl.updateUrl(search, options);
      }
    }
  };
}
var NuqsAdapter = createAdapterProvider(useNuqsNextAdapter);

export { NuqsAdapter };
