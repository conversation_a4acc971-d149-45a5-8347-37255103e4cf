// src/testing.ts
function isParserBijective(parser, serialized, input) {
  testSerializeThenParse(parser, input);
  testParseThenSerialize(parser, serialized);
  if (parser.serialize(input) !== serialized) {
    throw new Error(
      `[nuqs] parser.serialize does not match expected serialized value
  Expected: '${serialized}'
  Received: '${parser.serialize(input)}'
  `
    );
  }
  const parsed = parser.parse(serialized);
  if (!parser.eq(parsed, input)) {
    throw new Error(
      `[nuqs] parser.parse does not match expected input value
  Expected: ${input}
  Received: ${parsed}
  `
    );
  }
  return true;
}
function testSerializeThenParse(parser, input) {
  const serialized = parser.serialize(input);
  const parsed = parser.parse(serialized);
  if (parsed === null) {
    throw new Error(
      `[nuqs] testSerializeThenParse: parsed value is null (when parsing ${serialized} serialized from ${input})`
    );
  }
  if (!parser.eq(input, parsed)) {
    throw new Error(
      `[nuqs] parser is not bijective (in testSerializeThenParse)
  Expected value:         ${typeof input === "object" ? JSON.stringify(input) : input}
  Received parsed value:  ${typeof parsed === "object" ? JSON.stringify(parsed) : parsed}
  Serialized as: '${serialized}'
  `
    );
  }
  return true;
}
function testParseThenSerialize(parser, input) {
  const parsed = parser.parse(input);
  if (parsed === null) {
    throw new Error(
      `[nuqs] testParseThenSerialize: parsed value is null (when parsing ${input})`
    );
  }
  const serialized = parser.serialize(parsed);
  if (serialized !== input) {
    throw new Error(
      `[nuqs] parser is not bijective (in testParseThenSerialize)
  Expected query: '${input}'
  Received query: '${serialized}'
  Parsed value: ${parsed}
`
    );
  }
  return true;
}

export { isParserBijective, testParseThenSerialize, testSerializeThenParse };
