'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import VesselIcon from '../../vessels/vesel-icon'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'

interface VesselDisplayProps {
    vessels: any[]
    maxVisibleVessels?: number
    className?: string
}

export const VesselDisplay: React.FC<VesselDisplayProps> = ({
    vessels,
    maxVisibleVessels = 4,
    className = '',
}) => {
    const { getVesselWithIcon } = useVesselIconData()

    if (!vessels || vessels.length === 0) {
        return null
    }

    const visibleVessels = vessels.slice(0, maxVisibleVessels)
    const hiddenVessels = vessels.slice(maxVisibleVessels)
    const hasOverflow = hiddenVessels.length > 0

    return (
        <div className={`flex flex-row gap-2 ${className}`}>
            {/* Render visible vessels */}
            {visibleVessels.map((vessel: any) => {
                const vesselWithIcon = getVesselWithIcon(vessel.id, vessel)
                return (
                    <div
                        key={String(vessel.id)}
                        className="flex items-center text-start gap-2">
                        <Tooltip key={vessel.id}>
                            <TooltipTrigger className="shrink-0">
                                <VesselIcon vessel={vesselWithIcon} />
                            </TooltipTrigger>
                            <TooltipContent>{vessel.title}</TooltipContent>
                        </Tooltip>
                    </div>
                )
            })}

            {/* Render overflow button with popover */}
            {hasOverflow && (
                <Popover>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            className="!p-2 bg-transparent flex-none">
                            +{hiddenVessels.length} more
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-64 p-2">
                        <div className="space-y-1">
                            {hiddenVessels.map((vessel: any) => {
                                const vesselWithIcon = getVesselWithIcon(
                                    vessel.id,
                                    vessel,
                                )
                                return (
                                    <div
                                        key={vessel.id}
                                        className="px-2 py-1 text-sm hover:bg-muted rounded flex items-center gap-2">
                                        <div className="size-6 flex items-center justify-center flex-shrink-0 [&_img]:!size-6 [&_svg]:!size-6">
                                            <VesselIcon
                                                vessel={vesselWithIcon}
                                            />
                                        </div>
                                        {vessel.title}
                                    </div>
                                )
                            })}
                        </div>
                    </PopoverContent>
                </Popover>
            )}
        </div>
    )
}

export default VesselDisplay
