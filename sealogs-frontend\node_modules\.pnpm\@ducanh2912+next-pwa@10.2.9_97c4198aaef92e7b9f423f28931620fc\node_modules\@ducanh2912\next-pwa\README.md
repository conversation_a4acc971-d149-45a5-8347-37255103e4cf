# [PWA](https://web.dev/learn/pwa/) for [Next.js](https://nextjs.org/), powered by [Workbox](https://developer.chrome.com/docs/workbox).

This plugin is powered by [Workbox](https://developer.chrome.com/docs/workbox/) and other good stuff.

[![Build Size](https://img.shields.io/bundlephobia/minzip/@ducanh2912/next-pwa?label=Bundle%20size&style=flat&color=success)](https://bundlephobia.com/result?p=@ducanh2912/next-pwa)
[![Version](https://img.shields.io/npm/v/@ducanh2912/next-pwa?style=flat&color=success)](https://www.npmjs.com/package/@ducanh2912/next-pwa)

**NOTE:** If there's no specific reason to continue using `@ducanh2912/next-pwa`, consider migrating to [`@serwist/next`](https://serwist.pages.dev/docs/next), a part of [Serwist](https://serwist.pages.dev) (a Workbox fork).

---

## Getting Started

Visit https://ducanh-next-pwa.vercel.app/docs/next-pwa/getting-started to get started with `next-pwa`.

## Documentation

Visit https://ducanh-next-pwa.vercel.app/docs/next-pwa to see the documentation.

## Community

You can ask questions and suggest features. Please follow our [Code of Conduct](/CODE_OF_CONDUCT.md) when you communicate with other people.

## Contributing

Please see [our contributing guide](/CONTRIBUTING.md).

## Reference

1. [Google Workbox](https://developer.chrome.com/docs/workbox/what-is-workbox/)
2. [ServiceWorker, MessageChannel, & postMessage](https://ponyfoo.com/articles/serviceworker-messagechannel-postmessage) by [Nicolás Bevacqua](https://ponyfoo.com/contributors/ponyfoo)
3. [The service worker lifecycle](https://developers.google.com/web/fundamentals/primers/service-workers/lifecycle)
4. [6 Tips to make your iOS PWA feel like a native app](https://www.netguru.com/codestories/pwa-ios)
5. [Make your PWA available on Google Play Store](https://www.netguru.com/codestories/make-your-pwa-available-on-google-play-store)
