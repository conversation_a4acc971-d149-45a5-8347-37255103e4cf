"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetNextSSRApolloSingletons = exports.RemoveMultipartDirectivesLink = exports.DebounceMultipartResponsesLink = exports.SSRMultipartLink = exports.useBackgroundQuery = exports.useReadQuery = exports.useSuspenseQuery = exports.useQuery = exports.useFragment = exports.NextSSRApolloClient = exports.NextSSRInMemoryCache = exports.ApolloNextAppProvider = void 0;
var ApolloNextAppProvider_1 = require("./ApolloNextAppProvider");
Object.defineProperty(exports, "ApolloNextAppProvider", { enumerable: true, get: function () { return ApolloNextAppProvider_1.ApolloNextAppProvider; } });
var NextSSRInMemoryCache_1 = require("./NextSSRInMemoryCache");
Object.defineProperty(exports, "NextSSRInMemoryCache", { enumerable: true, get: function () { return NextSSRInMemoryCache_1.NextSSRInMemoryCache; } });
var NextSSRApolloClient_1 = require("./NextSSRApolloClient");
Object.defineProperty(exports, "NextSSRApolloClient", { enumerable: true, get: function () { return NextSSRApolloClient_1.NextSSRApolloClient; } });
var hooks_1 = require("./hooks");
Object.defineProperty(exports, "useFragment", { enumerable: true, get: function () { return hooks_1.useFragment; } });
Object.defineProperty(exports, "useQuery", { enumerable: true, get: function () { return hooks_1.useQuery; } });
Object.defineProperty(exports, "useSuspenseQuery", { enumerable: true, get: function () { return hooks_1.useSuspenseQuery; } });
Object.defineProperty(exports, "useReadQuery", { enumerable: true, get: function () { return hooks_1.useReadQuery; } });
Object.defineProperty(exports, "useBackgroundQuery", { enumerable: true, get: function () { return hooks_1.useBackgroundQuery; } });
var SSRMultipartLink_1 = require("./SSRMultipartLink");
Object.defineProperty(exports, "SSRMultipartLink", { enumerable: true, get: function () { return SSRMultipartLink_1.SSRMultipartLink; } });
var AccumulateMultipartResponsesLink_1 = require("./AccumulateMultipartResponsesLink");
Object.defineProperty(exports, "DebounceMultipartResponsesLink", { enumerable: true, get: function () { return AccumulateMultipartResponsesLink_1.AccumulateMultipartResponsesLink; } });
var RemoveMultipartDirectivesLink_1 = require("./RemoveMultipartDirectivesLink");
Object.defineProperty(exports, "RemoveMultipartDirectivesLink", { enumerable: true, get: function () { return RemoveMultipartDirectivesLink_1.RemoveMultipartDirectivesLink; } });
var testHelpers_1 = require("./testHelpers");
Object.defineProperty(exports, "resetNextSSRApolloSingletons", { enumerable: true, get: function () { return testHelpers_1.resetNextSSRApolloSingletons; } });
//# sourceMappingURL=index.js.map